# Landing Page Development - Clear Quote Pro

## Tech Stack

### Core Technologies
- **Framework**: Astro 5.x (Static Site Generation)
- **UI Components**: React 19.x (Islands Architecture)
- **Styling**: Tailwind CSS 4.x
- **Component Library**: shadcn/ui (shared package)
- **Animations**: anime.js 4.x
- **Forms**: React Hook Form + Zod
- **Analytics**: Simple Analytics
- **Database ORM**: Drizzle
- **Auth**: Better Auth
- **Monorepo**: Turborepo
- **Package Manager**: pnpm
- **Deployment**: Vercel

## Project Structure

```
clear-quote-pro/
├── apps/
│   ├── landing/                    # Astro landing page
│   │   ├── src/
│   │   │   ├── components/
│   │   │   │   ├── sections/      # Page sections
│   │   │   │   │   ├── Hero.tsx
│   │   │   │   │   ├── Services.tsx
│   │   │   │   │   ├── HowItWorks.tsx
│   │   │   │   │   ├── RentalOwnerSpotlight.tsx
│   │   │   │   │   ├── TrustIndicators.tsx
│   │   │   │   │   ├── PricingTransparency.tsx
│   │   │   │   │   ├── MaterialSelector.tsx
│   │   │   │   │   ├── FAQ.tsx
│   │   │   │   │   └── CTA.tsx
│   │   │   │   ├── ui/            # Local UI components
│   │   │   │   │   ├── AnimatedCounter.tsx
│   │   │   │   │   ├── ServiceCard.tsx
│   │   │   │   │   ├── PricingCalculator.tsx
│   │   │   │   │   └── TestimonialCard.tsx
│   │   │   │   └── forms/
│   │   │   │       ├── EarlyAccessForm.tsx
│   │   │   │       ├── ContactForm.tsx
│   │   │   │       └── ServiceQuoteForm.tsx
│   │   │   ├── layouts/
│   │   │   │   └── BaseLayout.astro
│   │   │   ├── pages/
│   │   │   │   ├── index.astro
│   │   │   │   ├── services/
│   │   │   │   │   └── [service].astro
│   │   │   │   ├── for-landlords.astro
│   │   │   │   ├── for-homeowners.astro
│   │   │   │   └── api/
│   │   │   │       └── early-access.ts
│   │   │   ├── styles/
│   │   │   │   └── global.css
│   │   │   └── lib/
│   │   │       ├── animations.ts
│   │   │       ├── constants.ts
│   │   │       └── utils.ts
│   │   ├── public/
│   │   │   ├── images/
│   │   │   ├── icons/
│   │   │   └── fonts/
│   │   ├── astro.config.mjs
│   │   ├── tailwind.config.ts
│   │   ├── tsconfig.json
│   │   └── package.json
│   │
│   ├── homeowner-app/              # Next.js homeowner dashboard
│   ├── vendor-dashboard/           # Next.js vendor dashboard  
│   └── admin-dashboard/            # Next.js admin panel
│
├── packages/
│   ├── ui/                        # Shared UI components
│   │   ├── src/
│   │   │   ├── components/
│   │   │   │   ├── button.tsx
│   │   │   │   ├── card.tsx
│   │   │   │   ├── dialog.tsx
│   │   │   │   ├── form.tsx
│   │   │   │   ├── input.tsx
│   │   │   │   ├── select.tsx
│   │   │   │   └── ...shadcn components
│   │   │   └── lib/
│   │   │       └── utils.ts
│   │   ├── tailwind.config.ts
│   │   ├── tsconfig.json
│   │   └── package.json
│   │
│   ├── database/                   # Shared database schemas
│   │   ├── src/
│   │   │   ├── schema/
│   │   │   │   ├── users.ts
│   │   │   │   ├── properties.ts
│   │   │   │   ├── services.ts
│   │   │   │   ├── vendors.ts
│   │   │   │   └── jobs.ts
│   │   │   ├── migrations/
│   │   │   └── index.ts
│   │   ├── drizzle.config.ts
│   │   └── package.json
│   │
│   ├── auth/                       # Shared auth configuration
│   │   ├── src/
│   │   │   ├── client.ts
│   │   │   ├── server.ts
│   │   │   └── middleware.ts
│   │   └── package.json
│   │
│   └── config/                     # Shared configurations
│       ├── eslint/
│       ├── typescript/
│       └── tailwind/
│
├── turbo.json
├── package.json
├── pnpm-workspace.yaml
└── README.md
```

## Component Architecture

### 1. Hero Section Component

```tsx
// apps/landing/src/components/sections/Hero.tsx

import { Button } from '@clear-quote-pro/ui/button'
import { Badge } from '@clear-quote-pro/ui/badge'
import { useAnimation } from '../../lib/animations'
import { useState, useEffect } from 'react'

interface HeroProps {
  userType?: 'homeowner' | 'landlord' | null
}

export function Hero({ userType }: HeroProps) {
  const { fadeIn, slideUp } = useAnimation()
  
  return (
    <section className="relative min-h-[90vh] flex items-center">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-white -z-10" />
      
      {/* Content Container */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto text-center">
          
          {/* Trust Badge */}
          <Badge className="mb-6" variant="secondary">
            Licensed • Insured • Background Checked
          </Badge>
          
          {/* Main Headline */}
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6">
            Your Properties.{' '}
            <span className="text-blue-600">Protected.</span>{' '}
            <span className="text-emerald-600">Professionally</span>{' '}
            Maintained.
          </h1>
          
          {/* Subheadline */}
          <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
            {userType === 'landlord' 
              ? 'Manage repairs across your entire portfolio with vetted pros, transparent pricing, and automated documentation.'
              : 'Stop calling around for quotes. Get instant, fair pricing from licensed, insured professionals.'
            }
          </p>
          
          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button size="lg" className="text-lg px-8 py-6">
              Get Started - Rental Properties
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8 py-6">
              Get Started - My Home
            </Button>
          </div>
          
          {/* Trust Indicators */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <TrustIndicator 
              value="2,500+" 
              label="Properties Serviced" 
              icon="home"
            />
            <TrustIndicator 
              value="$2M+" 
              label="Work Completed" 
              icon="dollar"
            />
            <TrustIndicator 
              value="4.8★" 
              label="Average Rating" 
              icon="star"
            />
            <TrustIndicator 
              value="100%" 
              label="Insured Vendors" 
              icon="shield"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
```

### 2. Services Grid Component

```tsx
// apps/landing/src/components/sections/Services.tsx

import { ServiceCard } from '../ui/ServiceCard'
import { services } from '../../lib/constants'
import { useAnimation } from '../../lib/animations'

export function Services() {
  const { staggerIn } = useAnimation()
  
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold mb-4">
            Every Service Your Property Needs
          </h2>
          <p className="text-xl text-gray-600">
            Pre-negotiated pricing for 20+ home services
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {services.map((service, index) => (
            <ServiceCard
              key={service.id}
              service={service}
              delay={index * 50}
            />
          ))}
        </div>
        
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            * Prices vary by region and project specifics. All prices include labor and standard materials.
          </p>
        </div>
      </div>
    </section>
  )
}
```

### 3. Animation Library Setup

```typescript
// apps/landing/src/lib/animations.ts

import anime from 'animejs'

export function useAnimation() {
  const fadeIn = (element: string | HTMLElement, delay = 0) => {
    anime({
      targets: element,
      opacity: [0, 1],
      translateY: [20, 0],
      duration: 600,
      delay,
      easing: 'easeOutCubic'
    })
  }
  
  const slideUp = (element: string | HTMLElement, delay = 0) => {
    anime({
      targets: element,
      translateY: [40, 0],
      opacity: [0, 1],
      duration: 800,
      delay,
      easing: 'easeOutQuart'
    })
  }
  
  const staggerIn = (elements: string | NodeList, delayIncrement = 100) => {
    anime({
      targets: elements,
      opacity: [0, 1],
      translateY: [30, 0],
      delay: anime.stagger(delayIncrement),
      duration: 600,
      easing: 'easeOutCubic'
    })
  }
  
  const countUp = (element: string | HTMLElement, start: number, end: number) => {
    const obj = { value: start }
    anime({
      targets: obj,
      value: end,
      duration: 2000,
      easing: 'easeInOutQuart',
      round: 1,
      update: function() {
        const el = typeof element === 'string' 
          ? document.querySelector(element) 
          : element
        if (el) el.innerHTML = obj.value.toLocaleString()
      }
    })
  }
  
  const parallax = (element: string | HTMLElement, speed = 0.5) => {
    window.addEventListener('scroll', () => {
      const scrolled = window.scrollY
      const el = typeof element === 'string' 
        ? document.querySelector(element) as HTMLElement
        : element
      if (el) {
        el.style.transform = `translateY(${scrolled * speed}px)`
      }
    })
  }
  
  return {
    fadeIn,
    slideUp,
    staggerIn,
    countUp,
    parallax
  }
}
```

### 4. Database Schema (Drizzle)

```typescript
// packages/database/src/schema/users.ts

import { pgTable, text, timestamp, uuid, boolean } from 'drizzle-orm/pg-core'

export const users = pgTable('users', {
  id: uuid('id').defaultRandom().primaryKey(),
  email: text('email').notNull().unique(),
  name: text('name').notNull(),
  phone: text('phone'),
  userType: text('user_type').notNull(), // 'homeowner' | 'landlord' | 'vendor'
  emailVerified: boolean('email_verified').default(false),
  phoneVerified: boolean('phone_verified').default(false),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
})

export const properties = pgTable('properties', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => users.id),
  address: text('address').notNull(),
  city: text('city').notNull(),
  state: text('state').notNull(),
  zip: text('zip').notNull(),
  propertyType: text('property_type'), // 'single_family' | 'multi_family' | 'condo'
  sqft: integer('sqft'),
  createdAt: timestamp('created_at').defaultNow()
})

export const earlyAccessSignups = pgTable('early_access_signups', {
  id: uuid('id').defaultRandom().primaryKey(),
  email: text('email').notNull().unique(),
  userType: text('user_type').notNull(),
  propertyCount: integer('property_count'),
  zipCode: text('zip_code'),
  createdAt: timestamp('created_at').defaultNow()
})
```

### 5. API Routes

```typescript
// apps/landing/src/pages/api/early-access.ts

import type { APIRoute } from 'astro'
import { db } from '@clear-quote-pro/database'
import { earlyAccessSignups } from '@clear-quote-pro/database/schema'
import { z } from 'zod'

const signupSchema = z.object({
  email: z.string().email(),
  userType: z.enum(['homeowner', 'landlord']),
  propertyCount: z.number().optional(),
  zipCode: z.string().length(5)
})

export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json()
    const validated = signupSchema.parse(body)
    
    await db.insert(earlyAccessSignups).values(validated)
    
    // Send to email service (SendGrid, Resend, etc.)
    // await sendWelcomeEmail(validated.email)
    
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Invalid request' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}
```

### 6. Astro Configuration

```javascript
// apps/landing/astro.config.mjs

import { defineConfig } from 'astro/config'
import react from '@astrojs/react'
import tailwind from '@astrojs/tailwind'
import sitemap from '@astrojs/sitemap'
import compress from 'astro-compress'

export default defineConfig({
  site: 'https://clearquotepro.com',
  integrations: [
    react(),
    tailwind({
      applyBaseStyles: false,
      configFile: './tailwind.config.ts'
    }),
    sitemap(),
    compress()
  ],
  output: 'static',
  build: {
    inlineStylesheets: 'auto'
  },
  vite: {
    optimizeDeps: {
      include: ['animejs']
    }
  }
})
```

### 7. Turbo Configuration

```json
// turbo.json
{
  "$schema": "https://turbo.build/schema.json",
  "globalDependencies": ["**/.env.*local"],
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": ["dist/**", ".next/**", "!.next/cache/**"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "lint": {
      "dependsOn": ["^lint"]
    },
    "type-check": {
      "dependsOn": ["^type-check"]
    }
  }
}
```

### 8. Package.json Root

```json
// package.json
{
  "name": "clear-quote-pro",
  "private": true,
  "scripts": {
    "dev": "turbo dev",
    "build": "turbo build",
    "preview": "turbo preview",
    "lint": "turbo lint",
    "type-check": "turbo type-check",
    "db:push": "pnpm --filter database db:push",
    "db:studio": "pnpm --filter database db:studio"
  },
  "devDependencies": {
    "turbo": "latest",
    "prettier": "latest",
    "eslint": "latest",
    "typescript": "^5.3.0"
  },
  "packageManager": "pnpm@8.0.0",
  "engines": {
    "node": ">=18.0.0"
  }
}
```

### 9. Landing Page Package.json

```json
// apps/landing/package.json
{
  "name": "@clear-quote-pro/landing",
  "type": "module",
  "version": "0.0.1",
  "scripts": {
    "dev": "astro dev",
    "build": "astro build",
    "preview": "astro preview",
    "lint": "eslint src",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "@astrojs/react": "^3.0.0",
    "@astrojs/tailwind": "^5.0.0",
    "@clear-quote-pro/ui": "workspace:*",
    "@clear-quote-pro/database": "workspace:*",
    "astro": "^4.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "animejs": "^3.2.1",
    "react-hook-form": "^7.48.0",
    "zod": "^3.22.0",
    "@hookform/resolvers": "^3.3.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@types/animejs": "^3.1.0",
    "tailwindcss": "^3.3.0",
    "typescript": "^5.3.0"
  }
}
```

## Environment Variables

```bash
# .env.local (apps/landing/)

# Database
DATABASE_URL="postgresql://user:password@localhost:5432/clearquotepro"

# Production Database (Neon)
DATABASE_URL_PROD="postgresql://user:<EMAIL>/clearquotepro"

# Simple Analytics
PUBLIC_SIMPLE_ANALYTICS_ID="your-id"

# API Keys
SIBI_PRO_API_KEY="your-sibi-pro-key"

# Email Service
SENDGRID_API_KEY="your-sendgrid-key"
EMAIL_FROM="<EMAIL>"
```

## Deployment Configuration

### Vercel Configuration

```json
// vercel.json
{
  "buildCommand": "pnpm build",
  "outputDirectory": "apps/landing/dist",
  "installCommand": "pnpm install",
  "framework": "astro"
}
```

## SEO & Performance

### Meta Tags Setup

```astro
<!-- apps/landing/src/layouts/BaseLayout.astro -->
---
import { ViewTransitions } from 'astro:transitions'

export interface Props {
  title?: string
  description?: string
  ogImage?: string
}

const { 
  title = 'Clear Quote Pro - Pre-Negotiated Home Services',
  description = 'Manage home repairs with pre-negotiated pricing from licensed contractors.',
  ogImage = '/og-image.png'
} = Astro.props
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    
    <title>{title}</title>
    <meta name="description" content={description} />
    
    <!-- Open Graph -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={ogImage} />
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={title} />
    <meta name="twitter:description" content={description} />
    <meta name="twitter:image" content={ogImage} />
    
    <!-- Simple Analytics -->
    <script async defer src="https://scripts.simpleanalytics.com/latest.js"></script>
    <noscript><img src="https://queue.simpleanalytics.com/noscript.gif" alt="" referrerpolicy="no-referrer-when-downgrade" /></noscript>
    
    <ViewTransitions />
  </head>
  <body>
    <slot />
  </body>
</html>
```

## Development Workflow

### Initial Setup

```bash
# Clone repository
git clone https://github.com/yourusername/clear-quote-pro.git
cd clear-quote-pro

# Install dependencies
pnpm install

# Setup database
pnpm db:push

# Start development
pnpm dev

# Landing page will be at http://localhost:4321
```

### Component Development

1. Create components in `packages/ui` for shared use
2. Import in landing page with `@clear-quote-pro/ui/component-name`
3. Use Storybook for component development (optional)

### Adding New Sections

1. Create component in `apps/landing/src/components/sections/`
2. Import in `apps/landing/src/pages/index.astro`
3. Add animations with `useAnimation` hook
4. Ensure responsive design with Tailwind

### Performance Optimization

1. Use Astro's Image component for optimization
2. Lazy load below-the-fold components
3. Minimize JavaScript bundles with React islands
4. Use static generation for all pages
5. Implement proper caching headers

## Testing Strategy

### Unit Tests
- Components with React Testing Library
- API routes with Vitest
- Schema validation with Zod

### E2E Tests
- Critical user flows with Playwright
- Form submissions
- Analytics tracking
- Responsive design

### Performance Testing
- Lighthouse CI in GitHub Actions
- Bundle size monitoring
- Core Web Vitals tracking

## Monitoring

### Analytics Events

```javascript
// Track with Simple Analytics
sa('goal', 'early_access_signup')
sa('goal', 'cta_clicked')
sa('goal', 'service_selected')
```

### Error Tracking
- Sentry for production errors
- LogRocket for session replay (optional)

## Security Considerations

1. **Form Validation**: Server-side with Zod
2. **Rate Limiting**: API routes with middleware
3. **CORS**: Properly configured for API
4. **CSP Headers**: Content Security Policy
5. **Input Sanitization**: XSS prevention
6. **HTTPS Only**: Enforce SSL

## Launch Checklist

- [ ] Domain configured
- [ ] SSL certificate active
- [ ] Database migrations run
- [ ] Environment variables set
- [ ] Analytics configured
- [ ] SEO meta tags verified
- [ ] OG images created
- [ ] Sitemap generated
- [ ] Robots.txt configured
- [ ] 404 page designed
- [ ] Forms tested
- [ ] Mobile responsive verified
- [ ] Performance optimized (95+ Lighthouse)
- [ ] Accessibility checked (WCAG 2.1)
- [ ] Legal pages added (Privacy, Terms)
- [ ] Contact information accurate
- [ ] Social media links working
- [ ] Email templates created
- [ ] Error tracking configured
- [ ] Backup strategy in place