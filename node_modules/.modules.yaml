hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@astrojs/compiler@2.12.2':
    '@astrojs/compiler': private
  '@astrojs/internal-helpers@0.7.1':
    '@astrojs/internal-helpers': private
  '@astrojs/markdown-remark@6.3.5':
    '@astrojs/markdown-remark': private
  '@astrojs/mdx@4.3.3(astro@5.12.8(@types/node@24.2.0)(jiti@2.5.1)(lightningcss@1.30.1)(rollup@4.46.2)(typescript@5.9.2))':
    '@astrojs/mdx': private
  '@astrojs/prism@3.3.0':
    '@astrojs/prism': private
  '@astrojs/react@4.3.0(@types/node@24.2.0)(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(jiti@2.5.1)(lightningcss@1.30.1)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@astrojs/react': private
  '@astrojs/telemetry@3.3.0':
    '@astrojs/telemetry': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.28.2':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@capsizecss/unpack@2.4.0':
    '@capsizecss/unpack': private
  '@esbuild/aix-ppc64@0.25.8':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.8':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.8':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.8':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.8':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.8':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.8':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.8':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.8':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.8':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.8':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.8':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.8':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.8':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.8':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.8':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.8':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.8':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.8':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.8':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.8':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.8':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.8':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.8':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.8':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.8':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.31.0(jiti@2.5.1))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.0':
    '@eslint/config-helpers': private
  '@eslint/core@0.15.1':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.31.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.3':
    '@eslint/plugin-kit': private
  '@floating-ui/core@1.7.3':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.3':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.5(react-dom@19.1.0(react@19.1.1))(react@19.1.1)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@fontsource-variable/dm-sans@5.2.6':
    '@fontsource-variable/dm-sans': private
  '@fontsource-variable/outfit@5.2.6':
    '@fontsource-variable/outfit': private
  '@hookform/resolvers@5.2.1(react-hook-form@7.62.0(react@19.1.1))':
    '@hookform/resolvers': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-arm64@0.33.5':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.33.5':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.0.4':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.0.4':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.0.4':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.0.5':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-s390x@1.0.4':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.0.4':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.0.4':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.33.5':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.33.5':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.33.5':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.33.5':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.33.5':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.33.5':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.33.5':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.33.5':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.33.5':
    '@img/sharp-win32-x64': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@mdx-js/mdx@3.1.0(acorn@8.15.0)':
    '@mdx-js/mdx': private
  '@next/eslint-plugin-next@15.4.2':
    '@next/eslint-plugin-next': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@oslojs/encoding@1.1.0':
    '@oslojs/encoding': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-accordion@1.2.11(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-accordion': private
  '@radix-ui/react-alert-dialog@1.1.14(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-alert-dialog': private
  '@radix-ui/react-arrow@1.1.7(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.0(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-arrow@1.1.7(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-avatar@1.1.10(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-avatar': private
  '@radix-ui/react-checkbox@1.3.2(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-checkbox': private
  '@radix-ui/react-collapsible@1.1.11(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.0(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-collapsible': private
  '@radix-ui/react-collapsible@1.1.11(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-collapsible': private
  '@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.0(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-context': private
  '@radix-ui/react-dialog@1.1.14(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-dialog': private
  '@radix-ui/react-direction@1.1.1(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.0(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-dropdown-menu@2.1.15(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-dropdown-menu': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.0(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.1(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-id': private
  '@radix-ui/react-label@2.1.7(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-label': private
  '@radix-ui/react-menu@2.1.15(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.0(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-menu@2.1.15(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-navigation-menu@1.2.13(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-navigation-menu': private
  '@radix-ui/react-popover@1.1.14(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-popover': private
  '@radix-ui/react-popper@1.2.7(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.0(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-popper@1.2.7(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.9(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.0(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-portal@1.1.9(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.0(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.0(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-radio-group@1.3.7(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-radio-group': private
  '@radix-ui/react-roving-focus@1.1.10(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.0(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-roving-focus@1.1.10(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-scroll-area@1.2.9(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-scroll-area': private
  '@radix-ui/react-select@2.2.5(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-select': private
  '@radix-ui/react-separator@1.1.7(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-separator': private
  '@radix-ui/react-slot@1.2.3(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-slot': private
  '@radix-ui/react-switch@1.2.5(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-switch': private
  '@radix-ui/react-tabs@1.1.12(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-tabs': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-is-hydrated@0.1.0(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-is-hydrated': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.0(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@rolldown/pluginutils@1.0.0-beta.27':
    '@rolldown/pluginutils': private
  '@rollup/pluginutils@5.2.0(rollup@4.46.2)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.46.2':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.46.2':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.46.2':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.46.2':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.46.2':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.46.2':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.46.2':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.46.2':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.46.2':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.46.2':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.46.2':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-ppc64-gnu@4.46.2':
    '@rollup/rollup-linux-ppc64-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.46.2':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.46.2':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.46.2':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.46.2':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.46.2':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.46.2':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.46.2':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.46.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@shikijs/core@3.9.2':
    '@shikijs/core': private
  '@shikijs/engine-javascript@3.9.2':
    '@shikijs/engine-javascript': private
  '@shikijs/engine-oniguruma@3.9.2':
    '@shikijs/engine-oniguruma': private
  '@shikijs/langs@3.9.2':
    '@shikijs/langs': private
  '@shikijs/themes@3.9.2':
    '@shikijs/themes': private
  '@shikijs/types@3.9.2':
    '@shikijs/types': private
  '@shikijs/vscode-textmate@10.0.2':
    '@shikijs/vscode-textmate': private
  '@standard-schema/utils@0.3.0':
    '@standard-schema/utils': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.11':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.11':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.11':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.11':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.11':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.11':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.11':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.11':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.11':
    '@tailwindcss/oxide': private
  '@tailwindcss/vite@4.1.11(vite@6.3.5(@types/node@24.2.0)(jiti@2.5.1)(lightningcss@1.30.1))':
    '@tailwindcss/vite': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.28.0':
    '@types/babel__traverse': private
  '@types/canvas-confetti@1.9.0':
    '@types/canvas-confetti': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/fontkit@2.0.8':
    '@types/fontkit': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/mdx@2.0.13':
    '@types/mdx': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/nlcst@2.0.3':
    '@types/nlcst': private
  '@types/node@24.2.0':
    '@types/node': private
  '@types/react-dom@19.1.7(@types/react@19.1.9)':
    '@types/react-dom': private
  '@types/react@19.1.9':
    '@types/react': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@typescript-eslint/eslint-plugin@8.37.0(@typescript-eslint/parser@8.37.0(eslint@9.31.0(jiti@2.5.1))(typescript@5.8.2))(eslint@9.31.0(jiti@2.5.1))(typescript@5.8.2)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.37.0(eslint@9.31.0(jiti@2.5.1))(typescript@5.8.2)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.37.0(typescript@5.8.2)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.37.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.37.0(typescript@5.8.2)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.37.0(eslint@9.31.0(jiti@2.5.1))(typescript@5.8.2)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.37.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.37.0(typescript@5.8.2)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.37.0(eslint@9.31.0(jiti@2.5.1))(typescript@5.8.2)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.37.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@vitejs/plugin-react@4.7.0(vite@6.3.5(@types/node@24.2.0)(jiti@2.5.1)(lightningcss@1.30.1))':
    '@vitejs/plugin-react': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-align@3.0.1:
    ansi-align: private
  ansi-regex@6.1.0:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  apps/landing:
    landing: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.9:
    array-includes: private
  array-iterate@2.0.1:
    array-iterate: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  astring@1.9.0:
    astring: private
  astro@5.12.8(@types/node@24.2.0)(jiti@2.5.1)(lightningcss@1.30.1)(rollup@4.46.2)(typescript@5.9.2):
    astro: private
  async-function@1.0.0:
    async-function: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axobject-query@4.1.0:
    axobject-query: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  base-64@1.0.0:
    base-64: private
  base64-js@1.5.1:
    base64-js: private
  blob-to-buffer@1.2.9:
    blob-to-buffer: private
  boxen@8.0.1:
    boxen: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  brotli@1.3.3:
    brotli: private
  browserslist@4.25.1:
    browserslist: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase@8.0.0:
    camelcase: private
  caniuse-lite@1.0.30001731:
    caniuse-lite: private
  canvas-confetti@1.9.3:
    canvas-confetti: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@3.0.0:
    character-entities-legacy: private
  character-entities@2.0.2:
    character-entities: private
  character-reference-invalid@2.0.1:
    character-reference-invalid: private
  chokidar@4.0.3:
    chokidar: private
  chownr@3.0.0:
    chownr: private
  ci-info@4.3.0:
    ci-info: private
  class-variance-authority@0.7.1:
    class-variance-authority: private
  cli-boxes@3.0.0:
    cli-boxes: private
  clone@2.1.2:
    clone: private
  clsx@2.1.1:
    clsx: private
  cmdk@1.1.1(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    cmdk: private
  collapse-white-space@2.1.0:
    collapse-white-space: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  common-ancestor-path@1.0.1:
    common-ancestor-path: private
  concat-map@0.0.1:
    concat-map: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-es@1.2.2:
    cookie-es: private
  cookie@1.0.2:
    cookie: private
  cross-fetch@3.2.0:
    cross-fetch: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crossws@0.3.5:
    crossws: private
  css-tree@3.1.0:
    css-tree: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  decode-named-character-reference@1.2.0:
    decode-named-character-reference: private
  deep-is@0.1.4:
    deep-is: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  defu@6.1.4:
    defu: private
  dequal@2.0.3:
    dequal: private
  destr@2.0.5:
    destr: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  deterministic-object-hash@2.0.2:
    deterministic-object-hash: private
  devalue@5.1.1:
    devalue: private
  devlop@1.1.0:
    devlop: private
  dfa@1.2.0:
    dfa: private
  diff@5.2.0:
    diff: private
  dlv@1.1.3:
    dlv: private
  doctrine@2.1.0:
    doctrine: private
  dotenv@16.0.3:
    dotenv: private
  dset@3.1.4:
    dset: private
  dunder-proto@1.0.1:
    dunder-proto: private
  electron-to-chromium@1.5.198:
    electron-to-chromium: private
  emoji-regex@10.4.0:
    emoji-regex: private
  enhanced-resolve@5.18.3:
    enhanced-resolve: private
  entities@6.0.1:
    entities: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esast-util-from-estree@2.0.0:
    esast-util-from-estree: private
  esast-util-from-js@2.0.1:
    esast-util-from-js: private
  esbuild@0.25.8:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-config-prettier@10.1.1(eslint@9.31.0(jiti@2.5.1)):
    eslint-config-prettier: private
  eslint-plugin-only-warn@1.1.0:
    eslint-plugin-only-warn: private
  eslint-plugin-react-hooks@5.2.0(eslint@9.31.0(jiti@2.5.1)):
    eslint-plugin-react-hooks: private
  eslint-plugin-react@7.37.5(eslint@9.31.0(jiti@2.5.1)):
    eslint-plugin-react: private
  eslint-plugin-turbo@2.5.0(eslint@9.31.0(jiti@2.5.1))(turbo@2.5.5):
    eslint-plugin-turbo: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  eslint@9.31.0(jiti@2.5.1):
    eslint: private
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-util-attach-comments@3.0.0:
    estree-util-attach-comments: private
  estree-util-build-jsx@3.0.1:
    estree-util-build-jsx: private
  estree-util-is-identifier-name@3.0.0:
    estree-util-is-identifier-name: private
  estree-util-scope@1.0.0:
    estree-util-scope: private
  estree-util-to-js@2.0.0:
    estree-util-to-js: private
  estree-util-visit@2.0.0:
    estree-util-visit: private
  estree-walker@3.0.3:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  eventemitter3@5.0.1:
    eventemitter3: private
  extend@3.0.2:
    extend: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.1:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  flattie@1.1.1:
    flattie: private
  fontace@0.3.0:
    fontace: private
  fontkit@2.0.4:
    fontkit: private
  for-each@0.3.5:
    for-each: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-proto@1.0.1:
    get-proto: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  github-slugger@2.0.0:
    github-slugger: private
  glob-parent@6.0.2:
    glob-parent: private
  globals@16.3.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  h3@1.15.4:
    h3: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hast-util-from-html@2.0.3:
    hast-util-from-html: private
  hast-util-from-parse5@8.0.3:
    hast-util-from-parse5: private
  hast-util-is-element@3.0.0:
    hast-util-is-element: private
  hast-util-parse-selector@4.0.0:
    hast-util-parse-selector: private
  hast-util-raw@9.1.0:
    hast-util-raw: private
  hast-util-to-estree@3.1.3:
    hast-util-to-estree: private
  hast-util-to-html@9.0.5:
    hast-util-to-html: private
  hast-util-to-jsx-runtime@2.3.6:
    hast-util-to-jsx-runtime: private
  hast-util-to-parse5@8.0.0:
    hast-util-to-parse5: private
  hast-util-to-text@4.0.2:
    hast-util-to-text: private
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: private
  hastscript@9.0.1:
    hastscript: private
  html-escaper@3.0.3:
    html-escaper: private
  html-void-elements@3.0.0:
    html-void-elements: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  import-meta-resolve@4.1.0:
    import-meta-resolve: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inline-style-parser@0.2.4:
    inline-style-parser: private
  internal-slot@1.1.0:
    internal-slot: private
  iron-webcrypto@1.2.1:
    iron-webcrypto: private
  is-alphabetical@2.0.1:
    is-alphabetical: private
  is-alphanumerical@2.0.1:
    is-alphanumerical: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-decimal@2.0.1:
    is-decimal: private
  is-docker@3.0.0:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hexadecimal@2.0.1:
    is-hexadecimal: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-wsl@3.1.0:
    is-wsl: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jiti@2.5.1:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  kleur@4.1.5:
    kleur: private
  levn@0.4.1:
    levn: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@10.4.3:
    lru-cache: private
  lru-cache@5.1.1:
    lru-cache: private
  lucide-react@0.537.0(react@19.1.1):
    lucide-react: private
  magic-string@0.30.17:
    magic-string: private
  magicast@0.3.5:
    magicast: private
  markdown-extensions@2.0.0:
    markdown-extensions: private
  markdown-table@3.0.4:
    markdown-table: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-definitions@6.0.0:
    mdast-util-definitions: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-mdx-expression@2.0.1:
    mdast-util-mdx-expression: private
  mdast-util-mdx-jsx@3.2.0:
    mdast-util-mdx-jsx: private
  mdast-util-mdx@3.0.0:
    mdast-util-mdx: private
  mdast-util-mdxjs-esm@2.0.1:
    mdast-util-mdxjs-esm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  mdn-data@2.12.2:
    mdn-data: private
  merge2@1.4.1:
    merge2: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-extension-mdx-expression@3.0.1:
    micromark-extension-mdx-expression: private
  micromark-extension-mdx-jsx@3.0.2:
    micromark-extension-mdx-jsx: private
  micromark-extension-mdx-md@2.0.0:
    micromark-extension-mdx-md: private
  micromark-extension-mdxjs-esm@3.0.0:
    micromark-extension-mdxjs-esm: private
  micromark-extension-mdxjs@3.0.0:
    micromark-extension-mdxjs: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-mdx-expression@2.0.3:
    micromark-factory-mdx-expression: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-events-to-acorn@2.0.3:
    micromark-util-events-to-acorn: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  minimatch@3.1.2:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  neotraverse@0.6.18:
    neotraverse: private
  next-themes@0.4.6(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    next-themes: private
  nlcst-to-string@4.0.0:
    nlcst-to-string: private
  node-fetch-native@1.6.7:
    node-fetch-native: private
  node-fetch@2.7.0:
    node-fetch: private
  node-mock-http@1.0.2:
    node-mock-http: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.values@1.2.1:
    object.values: private
  ofetch@1.4.1:
    ofetch: private
  ohash@2.0.11:
    ohash: private
  oniguruma-parser@0.12.1:
    oniguruma-parser: private
  oniguruma-to-es@4.3.3:
    oniguruma-to-es: private
  optionator@0.9.4:
    optionator: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@6.2.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-queue@8.1.0:
    p-queue: private
  p-timeout@6.1.4:
    p-timeout: private
  package-manager-detector@1.3.0:
    package-manager-detector: private
  packages/eslint-config:
    '@repo/eslint-config': public
  packages/typescript-config:
    '@repo/typescript-config': private
  packages/ui:
    '@repo/ui': private
  pako@0.2.9:
    pako: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@4.0.2:
    parse-entities: private
  parse-latin@7.0.0:
    parse-latin: private
  parse5@7.3.0:
    parse5: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prismjs@1.30.0:
    prismjs: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  property-information@7.1.0:
    property-information: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  radix3@1.1.2:
    radix3: private
  react-dom@19.1.1(react@19.1.1):
    react-dom: private
  react-hook-form@7.62.0(react@19.1.1):
    react-hook-form: private
  react-is@16.13.1:
    react-is: private
  react-refresh@0.17.0:
    react-refresh: private
  react-remove-scroll-bar@2.3.8(@types/react@19.1.9)(react@19.1.1):
    react-remove-scroll-bar: private
  react-remove-scroll@2.7.1(@types/react@19.1.9)(react@19.1.1):
    react-remove-scroll: private
  react-style-singleton@2.2.3(@types/react@19.1.9)(react@19.1.1):
    react-style-singleton: private
  react@19.1.1:
    react: private
  readdirp@4.1.2:
    readdirp: private
  recma-build-jsx@1.0.0:
    recma-build-jsx: private
  recma-jsx@1.0.1(acorn@8.15.0):
    recma-jsx: private
  recma-parse@1.0.0:
    recma-parse: private
  recma-stringify@1.0.0:
    recma-stringify: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regex-recursion@6.0.2:
    regex-recursion: private
  regex-utilities@2.3.0:
    regex-utilities: private
  regex@6.0.1:
    regex: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  rehype-parse@9.0.1:
    rehype-parse: private
  rehype-raw@7.0.0:
    rehype-raw: private
  rehype-recma@1.0.0:
    rehype-recma: private
  rehype-stringify@10.0.1:
    rehype-stringify: private
  rehype@13.0.2:
    rehype: private
  remark-gfm@4.0.1:
    remark-gfm: private
  remark-mdx@3.1.0:
    remark-mdx: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.2:
    remark-rehype: private
  remark-smartypants@3.0.2:
    remark-smartypants: private
  remark-stringify@11.0.0:
    remark-stringify: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve@2.0.0-next.5:
    resolve: private
  restructure@3.0.2:
    restructure: private
  retext-latin@4.0.0:
    retext-latin: private
  retext-smartypants@6.2.0:
    retext-smartypants: private
  retext-stringify@4.0.0:
    retext-stringify: private
  retext@9.0.0:
    retext: private
  reusify@1.1.0:
    reusify: private
  rollup@4.46.2:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  scheduler@0.26.0:
    scheduler: private
  semver@6.3.1:
    semver: private
  semver@7.7.2:
    semver: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  sharp@0.33.5:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shiki@3.9.2:
    shiki: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sisteransi@1.0.5:
    sisteransi: private
  smol-toml@1.4.1:
    smol-toml: private
  sonner@2.0.7(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    sonner: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map@0.7.6:
    source-map: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  string-width@7.2.0:
    string-width: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  style-to-js@1.1.17:
    style-to-js: private
  style-to-object@1.0.9:
    style-to-object: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tailwind-merge@3.3.1:
    tailwind-merge: private
  tailwindcss@4.1.11:
    tailwindcss: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  tiny-inflate@1.0.3:
    tiny-inflate: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tr46@0.0.3:
    tr46: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  ts-api-utils@2.1.0(typescript@5.8.2):
    ts-api-utils: private
  tsconfck@3.1.6(typescript@5.9.2):
    tsconfck: private
  tslib@2.8.1:
    tslib: private
  turbo-darwin-64@2.5.5:
    turbo-darwin-64: private
  turbo-darwin-arm64@2.5.5:
    turbo-darwin-arm64: private
  turbo-linux-64@2.5.5:
    turbo-linux-64: private
  turbo-linux-arm64@2.5.5:
    turbo-linux-arm64: private
  turbo-windows-64@2.5.5:
    turbo-windows-64: private
  turbo-windows-arm64@2.5.5:
    turbo-windows-arm64: private
  tw-animate-css@1.3.6:
    tw-animate-css: private
  type-check@0.4.0:
    type-check: private
  type-fest@4.41.0:
    type-fest: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typescript-eslint@8.37.0(eslint@9.31.0(jiti@2.5.1))(typescript@5.8.2):
    typescript-eslint: private
  ufo@1.6.1:
    ufo: private
  ultrahtml@1.6.0:
    ultrahtml: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  uncrypto@0.1.3:
    uncrypto: private
  undici-types@7.10.0:
    undici-types: private
  unicode-properties@1.4.1:
    unicode-properties: private
  unicode-trie@2.0.0:
    unicode-trie: private
  unified@11.0.5:
    unified: private
  unifont@0.5.2:
    unifont: private
  unist-util-find-after@5.0.0:
    unist-util-find-after: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-modify-children@4.0.0:
    unist-util-modify-children: private
  unist-util-position-from-estree@2.0.0:
    unist-util-position-from-estree: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-remove-position@5.0.0:
    unist-util-remove-position: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-children@3.0.0:
    unist-util-visit-children: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  unstorage@1.16.1:
    unstorage: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.3(@types/react@19.1.9)(react@19.1.1):
    use-callback-ref: private
  use-sidecar@1.1.3(@types/react@19.1.9)(react@19.1.1):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@19.1.1):
    use-sync-external-store: private
  vfile-location@5.0.3:
    vfile-location: private
  vfile-message@4.0.3:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  vite@6.3.5(@types/node@24.2.0)(jiti@2.5.1)(lightningcss@1.30.1):
    vite: private
  vitefu@1.1.1(vite@6.3.5(@types/node@24.2.0)(jiti@2.5.1)(lightningcss@1.30.1)):
    vitefu: private
  web-namespaces@2.0.1:
    web-namespaces: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-pm-runs@1.1.0:
    which-pm-runs: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  widest-line@5.0.0:
    widest-line: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@9.0.0:
    wrap-ansi: private
  xxhash-wasm@1.1.0:
    xxhash-wasm: private
  yallist@3.1.1:
    yallist: private
  yallist@5.0.0:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yocto-queue@1.2.1:
    yocto-queue: private
  yocto-spinner@0.2.3:
    yocto-spinner: private
  yoctocolors@2.1.1:
    yoctocolors: private
  zod-to-json-schema@3.24.6(zod@3.25.76):
    zod-to-json-schema: private
  zod-to-ts@1.2.0(typescript@5.9.2)(zod@3.25.76):
    zod-to-ts: private
  zod@4.0.15:
    zod: private
  zwitch@2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Thu, 07 Aug 2025 15:44:50 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/runtime@1.4.5'
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm64@0.25.8'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/linux-x64@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.25.8'
  - '@esbuild/win32-x64@0.25.8'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-darwin-x64@0.34.3'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.2.0'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.2.0'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-arm@1.2.0'
  - '@img/sharp-libvips-linux-ppc64@1.2.0'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-s390x@1.2.0'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.2.0'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm64@0.34.3'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-arm@0.34.3'
  - '@img/sharp-linux-ppc64@0.34.3'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-s390x@0.34.3'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linux-x64@0.34.3'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.34.3'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.34.3'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-wasm32@0.34.3'
  - '@img/sharp-win32-arm64@0.34.3'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@img/sharp-win32-ia32@0.34.3'
  - '@img/sharp-win32-x64@0.33.5'
  - '@img/sharp-win32-x64@0.34.3'
  - '@next/swc-darwin-x64@15.4.2'
  - '@next/swc-linux-arm64-gnu@15.4.2'
  - '@next/swc-linux-arm64-musl@15.4.2'
  - '@next/swc-linux-x64-gnu@15.4.2'
  - '@next/swc-linux-x64-musl@15.4.2'
  - '@next/swc-win32-arm64-msvc@15.4.2'
  - '@next/swc-win32-x64-msvc@15.4.2'
  - '@rollup/rollup-android-arm-eabi@4.46.2'
  - '@rollup/rollup-android-arm64@4.46.2'
  - '@rollup/rollup-darwin-x64@4.46.2'
  - '@rollup/rollup-freebsd-arm64@4.46.2'
  - '@rollup/rollup-freebsd-x64@4.46.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.2'
  - '@rollup/rollup-linux-arm64-gnu@4.46.2'
  - '@rollup/rollup-linux-arm64-musl@4.46.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.2'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-musl@4.46.2'
  - '@rollup/rollup-linux-s390x-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-musl@4.46.2'
  - '@rollup/rollup-win32-arm64-msvc@4.46.2'
  - '@rollup/rollup-win32-ia32-msvc@4.46.2'
  - '@rollup/rollup-win32-x64-msvc@4.46.2'
  - '@tailwindcss/oxide-android-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-x64@4.1.11'
  - '@tailwindcss/oxide-freebsd-x64@4.1.11'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.11'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.11'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.11'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.11'
  - '@tailwindcss/oxide-win32-x64-msvc@4.1.11'
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - lightningcss-win32-x64-msvc@1.30.1
  - turbo-darwin-64@2.5.5
  - turbo-linux-64@2.5.5
  - turbo-linux-arm64@2.5.5
  - turbo-windows-64@2.5.5
  - turbo-windows-arm64@2.5.5
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
