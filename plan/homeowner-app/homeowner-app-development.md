# Homeowner App Development - Clear Quote Pro

## Tech Stack

### Core Technologies
- **Framework**: Astro 5.x (Hybrid rendering)
- **UI Framework**: React 19.x (Islands architecture)
- **Styling**: Tailwind CSS 4.x
- **Component Library**: shadcn/ui (shared package)
- **State Management**: Zustand
- **Forms**: React Hook Form + Zod
- **Animations**: anime.js 4.x
- **Database ORM**: Drizzle
- **Authentication**: Better Auth
- **File Storage**: Cloudflare R2
- **Email**: Resend
- **Analytics**: Simple Analytics
- **Monitoring**: Sentry
- **Deployment**: Vercel

## Project Structure

```
apps/homeowner-app/
├── src/
│   ├── components/
│   │   ├── auth/
│   │   │   ├── LoginForm.tsx
│   │   │   ├── SignupForm.tsx
│   │   │   ├── VerificationForm.tsx
│   │   │   └── PasswordReset.tsx
│   │   ├── dashboard/
│   │   │   ├── DashboardLayout.tsx
│   │   │   ├── ProjectCards.tsx
│   │   │   ├── QuickActions.tsx
│   │   │   ├── ActivityFeed.tsx
│   │   │   └── StatsOverview.tsx
│   │   ├── properties/
│   │   │   ├── PropertyList.tsx
│   │   │   ├── PropertyCard.tsx
│   │   │   ├── PropertyForm.tsx
│   │   │   └── PropertyDetails.tsx
│   │   ├── projects/
│   │   │   ├── ServiceGrid.tsx
│   │   │   ├── ProjectWizard.tsx
│   │   │   ├── RoomConfiguration.tsx
│   │   │   ├── MaterialSelector.tsx
│   │   │   ├── PriceCalculator.tsx
│   │   │   └── ProjectSummary.tsx
│   │   ├── vendors/
│   │   │   ├── VendorList.tsx
│   │   │   ├── VendorCard.tsx
│   │   │   ├── VendorProfile.tsx
│   │   │   └── VendorComparison.tsx
│   │   ├── scheduling/
│   │   │   ├── CalendarView.tsx
│   │   │   ├── TimeSlotPicker.tsx
│   │   │   └── AppointmentConfirmation.tsx
│   │   ├── payments/
│   │   │   ├── PaymentForm.tsx
│   │   │   ├── PaymentSummary.tsx
│   │   │   ├── InvoiceView.tsx
│   │   │   └── PaymentHistory.tsx
│   │   ├── tracking/
│   │   │   ├── ProjectTimeline.tsx
│   │   │   ├── ProgressTracker.tsx
│   │   │   ├── PhotoGallery.tsx
│   │   │   └── CompletionReview.tsx
│   │   ├── messaging/
│   │   │   ├── MessageThread.tsx
│   │   │   ├── MessageComposer.tsx
│   │   │   └── NotificationCenter.tsx
│   │   └── shared/
│   │       ├── Navigation.tsx
│   │       ├── MobileNav.tsx
│   │       ├── LoadingStates.tsx
│   │       ├── ErrorBoundary.tsx
│   │       └── FileUpload.tsx
│   ├── layouts/
│   │   ├── BaseLayout.astro
│   │   ├── AppLayout.astro
│   │   └── AuthLayout.astro
│   ├── pages/
│   │   ├── index.astro                 # Redirect to dashboard
│   │   ├── auth/
│   │   │   ├── login.astro
│   │   │   ├── signup.astro
│   │   │   ├── verify.astro
│   │   │   └── reset-password.astro
│   │   ├── dashboard.astro
│   │   ├── properties/
│   │   │   ├── index.astro
│   │   │   ├── new.astro
│   │   │   └── [id]/
│   │   │       ├── index.astro
│   │   │       └── edit.astro
│   │   ├── projects/
│   │   │   ├── new.astro
│   │   │   ├── active.astro
│   │   │   ├── completed.astro
│   │   │   └── [id]/
│   │   │       ├── index.astro
│   │   │       ├── schedule.astro
│   │   │       ├── payment.astro
│   │   │       └── tracking.astro
│   │   ├── services/
│   │   │   ├── index.astro
│   │   │   └── [service].astro
│   │   ├── messages/
│   │   │   ├── index.astro
│   │   │   └── [threadId].astro
│   │   ├── account/
│   │   │   ├── profile.astro
│   │   │   ├── settings.astro
│   │   │   └── billing.astro
│   │   └── api/
│   │       ├── auth/
│   │       │   ├── [...all].ts         # Better Auth routes
│   │       │   └── verify-phone.ts
│   │       ├── properties/
│   │       │   ├── index.ts
│   │       │   └── [id].ts
│   │       ├── projects/
│   │       │   ├── create.ts
│   │       │   ├── estimate.ts
│   │       │   └── [id]/
│   │       │       ├── index.ts
│   │       │       └── status.ts
│   │       ├── vendors/
│   │       │   ├── available.ts
│   │       │   └── [id].ts
│   │       ├── payments/
│   │       │   ├── create-intent.ts
│   │       │   └── confirm.ts
│   │       ├── upload/
│   │       │   └── presigned-url.ts
│   │       └── sibi/
│   │           └── property-data.ts    # Mocked Sibi Pro API
│   ├── lib/
│   │   ├── auth.ts
│   │   ├── db.ts
│   │   ├── r2.ts
│   │   ├── email.ts
│   │   ├── constants.ts
│   │   ├── utils.ts
│   │   └── validations.ts
│   ├── stores/
│   │   ├── auth.store.ts
│   │   ├── project.store.ts
│   │   ├── property.store.ts
│   │   └── notification.store.ts
│   ├── hooks/
│   │   ├── useAuth.ts
│   │   ├── useProject.ts
│   │   ├── useNotifications.ts
│   │   └── useFileUpload.ts
│   ├── middleware/
│   │   └── index.ts
│   └── styles/
│       └── global.css
├── public/
│   ├── images/
│   └── icons/
├── astro.config.mjs
├── tailwind.config.ts
├── tsconfig.json
└── package.json
```

## Database Schema

```typescript
// packages/database/src/schema/homeowner-app.ts

import { 
  pgTable, 
  text, 
  timestamp, 
  uuid, 
  boolean, 
  integer, 
  decimal,
  jsonb,
  pgEnum 
} from 'drizzle-orm/pg-core'

// Enums
export const userTypeEnum = pgEnum('user_type', ['homeowner', 'landlord'])
export const propertyTypeEnum = pgEnum('property_type', ['single_family', 'condo', 'townhouse', 'multi_family'])
export const projectStatusEnum = pgEnum('project_status', [
  'draft',
  'pending_vendors',
  'vendor_selected',
  'scheduled',
  'in_progress',
  'completed',
  'cancelled'
])
export const paymentStatusEnum = pgEnum('payment_status', ['pending', 'processing', 'completed', 'failed', 'refunded'])

// Users table (extends Better Auth schema)
export const userProfiles = pgTable('user_profiles', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: text('user_id').notNull().unique(), // From Better Auth
  userType: userTypeEnum('user_type').notNull(),
  phone: text('phone'),
  phoneVerified: boolean('phone_verified').default(false),
  address: text('address'),
  city: text('city'),
  state: text('state'),
  zip: text('zip'),
  profileComplete: boolean('profile_complete').default(false),
  stripeCustomerId: text('stripe_customer_id'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
})

// Properties
export const properties = pgTable('properties', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: text('user_id').notNull(),
  nickname: text('nickname'),
  address: text('address').notNull(),
  city: text('city').notNull(),
  state: text('state').notNull(),
  zip: text('zip').notNull(),
  propertyType: propertyTypeEnum('property_type').notNull(),
  yearBuilt: integer('year_built'),
  sqft: integer('sqft'),
  bedrooms: integer('bedrooms'),
  bathrooms: decimal('bathrooms', { precision: 3, scale: 1 }),
  isPrimary: boolean('is_primary').default(false),
  isRental: boolean('is_rental').default(false),
  tenantInfo: jsonb('tenant_info'), // {name, phone, email}
  accessInstructions: text('access_instructions'),
  photos: jsonb('photos').$type<string[]>(),
  sibiPropertyId: text('sibi_property_id'), // From Sibi Pro API
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
})

// Services
export const services = pgTable('services', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: text('name').notNull(),
  slug: text('slug').notNull().unique(),
  category: text('category').notNull(),
  description: text('description'),
  basePrice: decimal('base_price', { precision: 10, scale: 2 }),
  priceUnit: text('price_unit'), // 'sqft', 'hour', 'project'
  requiresLicense: boolean('requires_license').default(false),
  icon: text('icon'),
  active: boolean('active').default(true),
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at').defaultNow()
})

// Projects
export const projects = pgTable('projects', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: text('user_id').notNull(),
  propertyId: uuid('property_id').references(() => properties.id),
  serviceId: uuid('service_id').references(() => services.id),
  status: projectStatusEnum('status').notNull().default('draft'),
  
  // Project Details
  title: text('title').notNull(),
  description: text('description'),
  scope: jsonb('scope'), // Detailed scope configuration
  measurements: jsonb('measurements'), // Room dimensions, counts, etc.
  materials: jsonb('materials'), // Selected materials and finishes
  photos: jsonb('photos').$type<string[]>(),
  
  // Pricing
  laborCost: decimal('labor_cost', { precision: 10, scale: 2 }),
  materialCost: decimal('material_cost', { precision: 10, scale: 2 }),
  platformFee: decimal('platform_fee', { precision: 10, scale: 2 }),
  totalCost: decimal('total_cost', { precision: 10, scale: 2 }),
  
  // Scheduling
  preferredStartDate: timestamp('preferred_start_date'),
  scheduledStartDate: timestamp('scheduled_start_date'),
  scheduledEndDate: timestamp('scheduled_end_date'),
  actualStartDate: timestamp('actual_start_date'),
  actualEndDate: timestamp('actual_end_date'),
  
  // Vendor
  selectedVendorId: uuid('selected_vendor_id'),
  vendorAcceptedAt: timestamp('vendor_accepted_at'),
  
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
})

// Project Vendors (vendors who can accept the project)
export const projectVendors = pgTable('project_vendors', {
  id: uuid('id').defaultRandom().primaryKey(),
  projectId: uuid('project_id').references(() => projects.id),
  vendorId: uuid('vendor_id').notNull(),
  status: text('status').notNull(), // 'pending', 'accepted', 'declined'
  availability: jsonb('availability'), // Available time slots
  responseAt: timestamp('response_at'),
  createdAt: timestamp('created_at').defaultNow()
})

// Payments
export const payments = pgTable('payments', {
  id: uuid('id').defaultRandom().primaryKey(),
  projectId: uuid('project_id').references(() => projects.id),
  userId: text('user_id').notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  status: paymentStatusEnum('status').notNull().default('pending'),
  milestone: text('milestone').notNull(), // 'deposit', 'pre_start', 'completion'
  
  // Payment Details (Mocked for now)
  paymentMethod: text('payment_method'), // 'card', 'ach', 'affirm'
  paymentIntentId: text('payment_intent_id'),
  receiptUrl: text('receipt_url'),
  
  paidAt: timestamp('paid_at'),
  createdAt: timestamp('created_at').defaultNow()
})

// Messages
export const messages = pgTable('messages', {
  id: uuid('id').defaultRandom().primaryKey(),
  projectId: uuid('project_id').references(() => projects.id),
  senderId: text('sender_id').notNull(),
  senderType: text('sender_type').notNull(), // 'homeowner', 'vendor', 'system'
  content: text('content').notNull(),
  attachments: jsonb('attachments').$type<string[]>(),
  readAt: timestamp('read_at'),
  createdAt: timestamp('created_at').defaultNow()
})

// Reviews
export const reviews = pgTable('reviews', {
  id: uuid('id').defaultRandom().primaryKey(),
  projectId: uuid('project_id').references(() => projects.id),
  reviewerId: text('reviewer_id').notNull(),
  revieweeId: text('reviewee_id').notNull(),
  reviewerType: text('reviewer_type').notNull(), // 'homeowner', 'vendor'
  
  overallRating: integer('overall_rating').notNull(), // 1-5
  qualityRating: integer('quality_rating'),
  timelinessRating: integer('timeliness_rating'),
  communicationRating: integer('communication_rating'),
  cleanlinessRating: integer('cleanliness_rating'),
  
  comment: text('comment'),
  photos: jsonb('photos').$type<string[]>(),
  
  createdAt: timestamp('created_at').defaultNow()
})

// Notifications
export const notifications = pgTable('notifications', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: text('user_id').notNull(),
  type: text('type').notNull(), // 'project_update', 'message', 'payment', etc.
  title: text('title').notNull(),
  content: text('content'),
  data: jsonb('data'), // Additional context
  readAt: timestamp('read_at'),
  createdAt: timestamp('created_at').defaultNow()
})
```

## Authentication Setup

```typescript
// apps/homeowner-app/src/lib/auth.ts

import { betterAuth } from 'better-auth'
import { drizzleAdapter } from 'better-auth/adapters/drizzle'
import { db } from './db'

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: 'pg'
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true
  },
  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!
    },
    facebook: {
      clientId: process.env.FACEBOOK_CLIENT_ID!,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET!
    }
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24 // 1 day
  },
  callbacks: {
    session: async ({ session, user }) => {
      // Add user profile data to session
      const profile = await db.query.userProfiles.findFirst({
        where: eq(userProfiles.userId, user.id)
      })
      
      return {
        ...session,
        user: {
          ...session.user,
          profileComplete: profile?.profileComplete || false,
          userType: profile?.userType
        }
      }
    }
  }
})

// Client-side auth helper
export const authClient = auth.client({
  baseURL: import.meta.env.PUBLIC_APP_URL
})
```

## Middleware Configuration

```typescript
// apps/homeowner-app/src/middleware/index.ts

import { defineMiddleware } from 'astro:middleware'
import { auth } from '../lib/auth'

const publicRoutes = ['/auth/login', '/auth/signup', '/auth/verify', '/auth/reset-password']

export const onRequest = defineMiddleware(async (context, next) => {
  const { pathname } = context.url
  
  // Skip auth for public routes
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return next()
  }
  
  // Skip auth for API routes (they handle their own auth)
  if (pathname.startsWith('/api/')) {
    return next()
  }
  
  // Check authentication
  const session = await auth.api.getSession({
    headers: context.request.headers
  })
  
  if (!session) {
    return context.redirect('/auth/login')
  }
  
  // Add session to locals
  context.locals.session = session
  context.locals.user = session.user
  
  // Check profile completion
  if (!session.user.profileComplete && pathname !== '/account/profile') {
    return context.redirect('/account/profile?complete=true')
  }
  
  return next()
})
```

## Key Components

### 1. Project Wizard Component

```tsx
// apps/homeowner-app/src/components/projects/ProjectWizard.tsx

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@clear-quote-pro/ui/button'
import { Progress } from '@clear-quote-pro/ui/progress'
import { useProjectStore } from '../../stores/project.store'
import { ServiceGrid } from './ServiceGrid'
import { RoomConfiguration } from './RoomConfiguration'
import { MaterialSelector } from './MaterialSelector'
import { PriceCalculator } from './PriceCalculator'
import { ProjectSummary } from './ProjectSummary'

const steps = [
  'Service Selection',
  'Project Scope',
  'Room Details',
  'Materials',
  'Review & Price'
]

export function ProjectWizard({ propertyId }: { propertyId: string }) {
  const [currentStep, setCurrentStep] = useState(0)
  const { createProject, currentProject } = useProjectStore()
  
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }
  
  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }
  
  const handleSubmit = async () => {
    try {
      const projectId = await createProject({
        ...currentProject,
        propertyId
      })
      
      // Navigate to vendor selection
      window.location.href = `/projects/${projectId}/vendors`
    } catch (error) {
      console.error('Failed to create project:', error)
    }
  }
  
  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex justify-between text-sm mb-2">
          {steps.map((step, index) => (
            <span
              key={step}
              className={`
                ${index <= currentStep ? 'text-blue-600 font-medium' : 'text-gray-400'}
              `}
            >
              {step}
            </span>
          ))}
        </div>
        <Progress value={(currentStep + 1) / steps.length * 100} />
      </div>
      
      {/* Step Content */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        {currentStep === 0 && <ServiceGrid onSelect={handleNext} />}
        {currentStep === 1 && <ProjectScope onComplete={handleNext} />}
        {currentStep === 2 && <RoomConfiguration onComplete={handleNext} />}
        {currentStep === 3 && <MaterialSelector onComplete={handleNext} />}
        {currentStep === 4 && <ProjectSummary onSubmit={handleSubmit} />}
      </div>
      
      {/* Navigation */}
      <div className="flex justify-between mt-6">
        <Button
          variant="outline"
          onClick={handleBack}
          disabled={currentStep === 0}
        >
          Back
        </Button>
        
        {currentStep < steps.length - 1 ? (
          <Button onClick={handleNext}>
            Continue
          </Button>
        ) : (
          <Button onClick={handleSubmit}>
            Find Vendors
          </Button>
        )}
      </div>
    </div>
  )
}
```

### 2. Cloudflare R2 File Upload

```typescript
// apps/homeowner-app/src/lib/r2.ts

import { S3Client, PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'

const R2_ACCOUNT_ID = process.env.R2_ACCOUNT_ID!
const R2_ACCESS_KEY_ID = process.env.R2_ACCESS_KEY_ID!
const R2_SECRET_ACCESS_KEY = process.env.R2_SECRET_ACCESS_KEY!
const R2_BUCKET_NAME = process.env.R2_BUCKET_NAME!

export const r2Client = new S3Client({
  region: 'auto',
  endpoint: `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: R2_ACCESS_KEY_ID,
    secretAccessKey: R2_SECRET_ACCESS_KEY
  }
})

export async function getUploadUrl(
  key: string,
  contentType: string,
  maxSize: number = 10 * 1024 * 1024 // 10MB default
) {
  const command = new PutObjectCommand({
    Bucket: R2_BUCKET_NAME,
    Key: key,
    ContentType: contentType,
    ContentLength: maxSize
  })
  
  const url = await getSignedUrl(r2Client, command, {
    expiresIn: 3600 // 1 hour
  })
  
  return url
}

// React hook for file uploads
// apps/homeowner-app/src/hooks/useFileUpload.ts

export function useFileUpload() {
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  
  const upload = async (file: File, folder: string) => {
    setUploading(true)
    
    try {
      // Get presigned URL from API
      const response = await fetch('/api/upload/presigned-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          filename: file.name,
          contentType: file.type,
          folder
        })
      })
      
      const { uploadUrl, fileUrl } = await response.json()
      
      // Upload directly to R2
      await fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type
        }
      })
      
      setProgress(100)
      return fileUrl
      
    } catch (error) {
      console.error('Upload failed:', error)
      throw error
    } finally {
      setUploading(false)
      setProgress(0)
    }
  }
  
  return { upload, uploading, progress }
}
```

### 3. Mocked Payment System

```typescript
// apps/homeowner-app/src/lib/payments.ts

interface PaymentIntent {
  id: string
  amount: number
  status: 'pending' | 'processing' | 'succeeded' | 'failed'
  clientSecret: string
}

// Mock payment service (replace with Stripe later)
export class MockPaymentService {
  async createPaymentIntent(amount: number, projectId: string): Promise<PaymentIntent> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return {
      id: `pi_${Math.random().toString(36).substr(2, 9)}`,
      amount,
      status: 'pending',
      clientSecret: `pi_secret_${Math.random().toString(36).substr(2, 9)}`
    }
  }
  
  async confirmPayment(
    paymentIntentId: string,
    paymentMethod: any
  ): Promise<PaymentIntent> {
    // Simulate processing
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Random success/failure for testing
    const success = Math.random() > 0.1
    
    return {
      id: paymentIntentId,
      amount: 0,
      status: success ? 'succeeded' : 'failed',
      clientSecret: ''
    }
  }
  
  async createPaymentMethod(card: any) {
    // Mock payment method creation
    return {
      id: `pm_${Math.random().toString(36).substr(2, 9)}`,
      card: {
        last4: card.number.slice(-4),
        brand: 'visa',
        exp_month: card.expMonth,
        exp_year: card.expYear
      }
    }
  }
}

export const paymentService = new MockPaymentService()
```

### 4. Mocked Sibi Pro Integration

```typescript
// apps/homeowner-app/src/lib/sibi-mock.ts

interface PropertyData {
  sqft: number
  yearBuilt: number
  bedrooms: number
  bathrooms: number
  lotSize: number
  taxAssessment: number
  lastSalePrice: number
  lastSaleDate: string
}

interface MaterialOption {
  id: string
  name: string
  brand: string
  pricePerUnit: number
  unit: string
  quality: 'economy' | 'standard' | 'premium'
  imageUrl: string
}

export class MockSibiProService {
  async getPropertyData(address: string): Promise<PropertyData> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // Return mock data based on address
    return {
      sqft: 1800 + Math.floor(Math.random() * 1000),
      yearBuilt: 1980 + Math.floor(Math.random() * 40),
      bedrooms: 3 + Math.floor(Math.random() * 2),
      bathrooms: 2 + Math.random(),
      lotSize: 6000 + Math.floor(Math.random() * 4000),
      taxAssessment: 250000 + Math.floor(Math.random() * 150000),
      lastSalePrice: 300000 + Math.floor(Math.random() * 200000),
      lastSaleDate: '2020-06-15'
    }
  }
  
  async getMaterialOptions(
    category: string,
    subCategory?: string
  ): Promise<MaterialOption[]> {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Mock material options
    const materials: Record<string, MaterialOption[]> = {
      paint: [
        {
          id: 'ppg-speedhide-ev',
          name: 'Speedhide Pro EV Zero',
          brand: 'PPG',
          pricePerUnit: 0.18,
          unit: 'sqft',
          quality: 'economy',
          imageUrl: '/images/materials/paint-economy.jpg'
        },
        {
          id: 'ppg-speedhide-zero',
          name: 'Speedhide Zero VOC',
          brand: 'PPG',
          pricePerUnit: 0.40,
          unit: 'sqft',
          quality: 'standard',
          imageUrl: '/images/materials/paint-standard.jpg'
        },
        {
          id: 'ppg-ultralast',
          name: 'UltraLast',
          brand: 'PPG',
          pricePerUnit: 0.50,
          unit: 'sqft',
          quality: 'premium',
          imageUrl: '/images/materials/paint-premium.jpg'
        }
      ],
      flooring: [
        {
          id: 'lvp-economy',
          name: 'CoreLuxe LVP',
          brand: 'CoreLuxe',
          pricePerUnit: 2.50,
          unit: 'sqft',
          quality: 'economy',
          imageUrl: '/images/materials/lvp-economy.jpg'
        },
        {
          id: 'lvp-standard',
          name: 'LifeProof LVP',
          brand: 'LifeProof',
          pricePerUnit: 3.50,
          unit: 'sqft',
          quality: 'standard',
          imageUrl: '/images/materials/lvp-standard.jpg'
        },
        {
          id: 'lvp-premium',
          name: 'COREtec Plus',
          brand: 'COREtec',
          pricePerUnit: 5.00,
          unit: 'sqft',
          quality: 'premium',
          imageUrl: '/images/materials/lvp-premium.jpg'
        }
      ]
    }
    
    return materials[category] || []
  }
  
  async calculateMaterialCost(
    materialId: string,
    quantity: number
  ): Promise<number> {
    const materials = await this.getMaterialOptions('paint')
    const material = materials.find(m => m.id === materialId)
    
    if (!material) throw new Error('Material not found')
    
    return material.pricePerUnit * quantity
  }
}

export const sibiService = new MockSibiProService()
```

## API Routes

### Authentication Routes

```typescript
// apps/homeowner-app/src/pages/api/auth/[...all].ts

import { auth } from '../../../lib/auth'

export const ALL = auth.handler
```

### Project Creation API

```typescript
// apps/homeowner-app/src/pages/api/projects/create.ts

import type { APIRoute } from 'astro'
import { db } from '../../../lib/db'
import { projects } from '@clear-quote-pro/database/schema'
import { z } from 'zod'

const createProjectSchema = z.object({
  propertyId: z.string().uuid(),
  serviceId: z.string().uuid(),
  title: z.string(),
  scope: z.object({
    rooms: z.array(z.object({
      name: z.string(),
      length: z.number(),
      width: z.number(),
      height: z.number(),
      photos: z.array(z.string()).optional()
    })),
    options: z.record(z.any())
  }),
  materials: z.record(z.any()),
  preferredStartDate: z.string().optional()
})

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const session = locals.session
    if (!session) {
      return new Response('Unauthorized', { status: 401 })
    }
    
    const body = await request.json()
    const data = createProjectSchema.parse(body)
    
    // Calculate pricing
    const pricing = await calculateProjectPrice(data)
    
    // Create project
    const [project] = await db.insert(projects).values({
      userId: session.user.id,
      propertyId: data.propertyId,
      serviceId: data.serviceId,
      title: data.title,
      scope: data.scope,
      materials: data.materials,
      laborCost: pricing.labor,
      materialCost: pricing.materials,
      platformFee: pricing.fee,
      totalCost: pricing.total,
      preferredStartDate: data.preferredStartDate 
        ? new Date(data.preferredStartDate) 
        : null,
      status: 'pending_vendors'
    }).returning()
    
    // Notify available vendors
    await notifyVendors(project.id)
    
    return new Response(JSON.stringify({ project }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('Project creation failed:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
}

async function calculateProjectPrice(data: any) {
  // Price calculation logic
  const sqft = data.scope.rooms.reduce((total: number, room: any) => {
    return total + (room.length * room.width)
  }, 0)
  
  const laborRate = 1.50 // Base rate per sqft
  const labor = sqft * laborRate
  
  // Material costs from selections
  const materials = sqft * 0.40 // Using standard material rate
  
  const subtotal = labor + materials
  const fee = subtotal * 0.20 // 20% platform fee
  
  return {
    labor,
    materials,
    fee,
    total: subtotal + fee
  }
}
```

## State Management

```typescript
// apps/homeowner-app/src/stores/project.store.ts

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface ProjectState {
  currentProject: any
  projects: any[]
  
  // Actions
  setCurrentProject: (project: any) => void
  updateProjectField: (field: string, value: any) => void
  createProject: (data: any) => Promise<string>
  fetchProjects: () => Promise<void>
}

export const useProjectStore = create<ProjectState>()(
  persist(
    (set, get) => ({
      currentProject: null,
      projects: [],
      
      setCurrentProject: (project) => set({ currentProject: project }),
      
      updateProjectField: (field, value) => set((state) => ({
        currentProject: {
          ...state.currentProject,
          [field]: value
        }
      })),
      
      createProject: async (data) => {
        const response = await fetch('/api/projects/create', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        })
        
        const { project } = await response.json()
        
        set((state) => ({
          projects: [...state.projects, project]
        }))
        
        return project.id
      },
      
      fetchProjects: async () => {
        const response = await fetch('/api/projects')
        const { projects } = await response.json()
        set({ projects })
      }
    }),
    {
      name: 'project-store'
    }
  )
)
```

## Astro Configuration

```javascript
// apps/homeowner-app/astro.config.mjs

import { defineConfig } from 'astro/config'
import react from '@astrojs/react'
import tailwind from '@astrojs/tailwind'
import vercel from '@astrojs/vercel/serverless'

export default defineConfig({
  output: 'hybrid',
  adapter: vercel(),
  integrations: [
    react(),
    tailwind({
      applyBaseStyles: false,
      configFile: './tailwind.config.ts'
    })
  ],
  vite: {
    optimizeDeps: {
      include: ['animejs', 'zustand']
    }
  }
})
```

## Environment Variables

```bash
# .env.local

# Database
DATABASE_URL="postgresql://user:password@localhost:5432/clearquotepro"
DATABASE_URL_PROD="postgresql://user:<EMAIL>/clearquotepro"

# Auth
AUTH_SECRET="your-auth-secret"
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
FACEBOOK_CLIENT_ID="your-facebook-client-id"
FACEBOOK_CLIENT_SECRET="your-facebook-client-secret"

# Cloudflare R2
R2_ACCOUNT_ID="your-account-id"
R2_ACCESS_KEY_ID="your-access-key"
R2_SECRET_ACCESS_KEY="your-secret-key"
R2_BUCKET_NAME="clearquotepro-uploads"
R2_PUBLIC_URL="https://uploads.clearquotepro.com"

# Email
RESEND_API_KEY="your-resend-key"
EMAIL_FROM="<EMAIL>"

# SMS (Twilio)
TWILIO_ACCOUNT_SID="your-twilio-sid"
TWILIO_AUTH_TOKEN="your-twilio-token"
TWILIO_PHONE_NUMBER="+**********"

# Analytics
PUBLIC_SIMPLE_ANALYTICS_ID="your-analytics-id"

# Monitoring
SENTRY_DSN="your-sentry-dsn"

# Public URLs
PUBLIC_APP_URL="http://localhost:4322"
PUBLIC_LANDING_URL="http://localhost:4321"
```

## Testing Strategy

### Unit Tests
```json
{
  "scripts": {
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage"
  }
}
```

### E2E Tests with Playwright
```typescript
// tests/e2e/project-creation.spec.ts

import { test, expect } from '@playwright/test'

test('complete project creation flow', async ({ page }) => {
  // Login
  await page.goto('/auth/login')
  await page.fill('[name="email"]', '<EMAIL>')
  await page.fill('[name="password"]', 'password')
  await page.click('[type="submit"]')
  
  // Navigate to new project
  await page.waitForURL('/dashboard')
  await page.click('text=Start New Project')
  
  // Select service
  await page.click('text=Painting')
  
  // Configure rooms
  await page.fill('[name="rooms"]', '3')
  await page.click('text=Continue')
  
  // Add room details
  await page.fill('[name="room-1-length"]', '14')
  await page.fill('[name="room-1-width"]', '12')
  
  // Continue through flow
  await page.click('text=Continue')
  
  // Verify summary
  await expect(page.locator('.project-total')).toContainText('$')
})
```

## Performance Optimization

### 1. Image Optimization
- Use Astro's Image component
- Lazy load images below fold
- Generate responsive sizes
- WebP with fallbacks

### 2. Code Splitting
- Dynamic imports for heavy components
- Route-based splitting
- Vendor chunk optimization

### 3. Caching Strategy
- Static assets: 1 year
- API responses: Appropriate cache headers
- Service Worker for offline support

## Security Measures

### 1. Input Validation
- Zod schemas for all inputs
- SQL injection prevention via Drizzle
- XSS protection

### 2. Authentication
- Secure session management
- CSRF protection
- Rate limiting on auth endpoints

### 3. File Upload Security
- File type validation
- Size limits
- Virus scanning (future)
- Secure URLs with expiration

## Deployment Checklist

- [ ] Environment variables configured
- [ ] Database migrations run
- [ ] R2 bucket configured
- [ ] Email service verified
- [ ] Auth providers configured
- [ ] SSL certificates active
- [ ] Monitoring configured
- [ ] Error tracking enabled
- [ ] Analytics installed
- [ ] Performance tested
- [ ] Security audit completed
- [ ] Accessibility tested
- [ ] Mobile responsiveness verified
- [ ] Browser compatibility checked
- [ ] Load testing completed