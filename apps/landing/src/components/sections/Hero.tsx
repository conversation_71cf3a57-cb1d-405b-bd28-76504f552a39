import { But<PERSON> } from '@repo/ui/button';
import { Badge } from '@repo/ui/badge';
import { ArrowR<PERSON>, Shield, Clock, DollarSign, CheckCircle, Sparkles, Home, Users } from 'lucide-react';
import { useEffect, useState } from 'react';

export default function Hero() {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  return (
    <section className="relative min-h-screen flex items-center overflow-hidden">
      {/* Advanced background with gradients and patterns */}
      <div className="absolute inset-0 -z-10">
        {/* Primary gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-cream via-background to-sage/10" />
        
        {/* Animated gradient orbs */}
        <div className="absolute top-0 -left-40 w-80 h-80 bg-forest/20 rounded-full blur-3xl animate-pulse-slow" />
        <div className="absolute top-40 -right-40 w-96 h-96 bg-teal/20 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '2s' }} />
        <div className="absolute -bottom-20 left-1/2 -translate-x-1/2 w-[40rem] h-80 bg-terracotta/15 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '4s' }} />
        
        {/* Subtle grid pattern */}
        <div className="absolute inset-0 grid-pattern opacity-[0.02]" />
        
        {/* Noise texture for depth */}
        <div className="absolute inset-0 noise-texture opacity-30" />
      </div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-32">
        <div className="max-w-6xl mx-auto">
          {/* Animated trust badge */}
          <div className={`flex items-center justify-center gap-2 mb-10 transition-all duration-1000 ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4'}`}>
            <Badge variant="secondary" className="px-4 py-1.5 bg-forest/10 text-forest border-forest/20 hover:bg-forest/15 transition-colors">
              <Shield className="w-3.5 h-3.5 mr-2" />
              <span className="text-sm font-medium">Licensed & Insured Professionals Only</span>
              <Sparkles className="w-3.5 h-3.5 ml-2 text-teal" />
            </Badge>
          </div>
          
          {/* Main headline with gradient text */}
          <h1 className={`text-center mb-8 leading-[1.05] transition-all duration-1000 delay-100 ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <span className="block text-foreground">Home Repairs</span>
            <span className="block gradient-text mt-2">Done Right.</span>
            <span className="block text-terracotta/80 text-5xl md:text-6xl lg:text-7xl mt-2">First Time.</span>
          </h1>
          
          {/* Enhanced subheadline */}
          <p className={`text-xl md:text-2xl text-muted-foreground text-center mb-12 max-w-3xl mx-auto leading-relaxed transition-all duration-1000 delay-200 ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            Skip the endless quotes and negotiations. Get 
            <span className="text-forest font-semibold"> transparent pricing </span> 
            from 
            <span className="text-teal font-semibold"> pre-vetted contractors</span>. 
            Your home deserves better than the runaround.
          </p>
          
          {/* Enhanced CTA Buttons with hover effects */}
          <div className={`flex flex-col sm:flex-row gap-4 justify-center mb-16 transition-all duration-1000 delay-300 ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <Button 
              size="lg" 
              className="text-base px-8 py-6 bg-forest hover:bg-forest-light shadow-forest hover-lift group transition-all duration-300"
            >
              <Home className="w-4 h-4 mr-2 group-hover:rotate-12 transition-transform" />
              See Your Service Prices
              <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="text-base px-8 py-6 border-forest/30 text-forest hover:bg-forest/5 hover:border-forest/50 hover-lift transition-all duration-300"
            >
              <Users className="w-4 h-4 mr-2" />
              Join as Contractor
            </Button>
          </div>
          
          {/* Service preview cards */}
          <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 mb-16 transition-all duration-1000 delay-400 ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <ServiceCard 
              title="Plumbing" 
              price="From $89"
              description="Leaks, clogs, fixtures"
              gradient="gradient-forest"
            />
            <ServiceCard 
              title="Electrical" 
              price="From $115"
              description="Outlets, switches, lighting"
              gradient="gradient-ocean"
              featured
            />
            <ServiceCard 
              title="HVAC" 
              price="From $125"
              description="AC, heating, maintenance"
              gradient="gradient-warm"
            />
          </div>
          
          {/* Enhanced trust metrics with glassmorphism */}
          <div className={`glass rounded-2xl p-8 transition-all duration-1000 delay-500 ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <TrustMetric 
                value="$5M+" 
                label="Work Completed" 
                icon={<DollarSign className="w-5 h-5" />}
                color="text-forest"
              />
              <TrustMetric 
                value="24hr" 
                label="Average Response" 
                icon={<Clock className="w-5 h-5" />}
                color="text-teal"
              />
              <TrustMetric 
                value="4.9" 
                label="Customer Rating" 
                subValue="/5.0"
                color="text-terracotta"
              />
              <TrustMetric 
                value="500+" 
                label="Verified Contractors" 
                icon={<CheckCircle className="w-5 h-5" />}
                color="text-forest"
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Floating decorative elements */}
      <div className="absolute top-1/3 left-10 w-20 h-20 rounded-full bg-gradient-to-br from-forest/20 to-teal/20 blur-xl animate-float" />
      <div className="absolute bottom-1/3 right-10 w-32 h-32 rounded-full bg-gradient-to-br from-terracotta/20 to-sage/20 blur-xl animate-float" style={{ animationDelay: '3s' }} />
    </section>
  );
}

function ServiceCard({ 
  title, 
  price, 
  description, 
  gradient,
  featured = false 
}: { 
  title: string; 
  price: string; 
  description: string;
  gradient: string;
  featured?: boolean;
}) {
  return (
    <div className={`relative group hover-lift transition-all duration-300 ${featured ? 'md:-mt-4' : ''}`}>
      <div className={`absolute inset-0 ${gradient} opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500`} />
      <div className={`relative p-6 rounded-xl border ${featured ? 'border-teal/30 bg-teal/5' : 'border-border bg-card'} hover:border-forest/30 transition-colors`}>
        {featured && (
          <Badge className="absolute -top-3 right-4 bg-teal text-white border-0">
            Most Popular
          </Badge>
        )}
        <h3 className="text-lg font-semibold mb-1">{title}</h3>
        <p className="text-2xl font-bold text-forest mb-2">{price}</p>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
    </div>
  );
}

function TrustMetric({ 
  value, 
  label, 
  icon,
  subValue,
  color = "text-foreground"
}: { 
  value: string; 
  label: string; 
  icon?: React.ReactNode;
  subValue?: string;
  color?: string;
}) {
  return (
    <div className="text-center group hover-scale transition-transform duration-300">
      <div className="flex items-center justify-center gap-2 mb-2">
        {icon && <span className={`${color} opacity-60 group-hover:opacity-100 transition-opacity`}>{icon}</span>}
        <span className={`text-2xl md:text-3xl font-bold ${color}`}>
          {value}
          {subValue && <span className="text-lg opacity-60">{subValue}</span>}
        </span>
      </div>
      <p className="text-sm text-muted-foreground">{label}</p>
    </div>
  );
}