{"name": "@fontsource-variable/outfit", "version": "5.2.6", "description": "Self-host the Outfit font in a neatly bundled NPM package.", "main": "index.css", "publishConfig": {"access": "public"}, "keywords": ["fontsource", "font", "font family", "google fonts", "outfit", "Outfit", "css", "sass", "front-end", "web", "typeface", "variable"], "exports": {".": {"sass": "./index.css", "default": "./index.css"}, "./LICENSE": "./LICENSE", "./*": {"sass": "./*.css", "default": "./*.css"}, "./*.css": {"sass": "./*.css", "default": "./*.css"}, "./files/*": {"sass": "./files/*", "default": "./files/*"}, "./files/*.woff": {"sass": "./files/*.woff", "default": "./files/*.woff"}, "./files/*.woff2": {"sass": "./files/*.woff2", "default": "./files/*.woff2"}, "./package.json": "./package.json", "./metadata.json": "./metadata.json", "./unicode.json": "./unicode.json", "./scss": {"sass": "./scss/metadata.scss"}}, "author": "Google Inc.", "license": "OFL-1.1", "homepage": "https://fontsource.org/fonts/outfit", "funding": "https://github.com/sponsors/ayuhito", "repository": {"type": "git", "url": "git+https://github.com/fontsource/font-files.git", "directory": "fonts/variable/outfit"}, "publishHash": "0e2bf68a692726b6"}