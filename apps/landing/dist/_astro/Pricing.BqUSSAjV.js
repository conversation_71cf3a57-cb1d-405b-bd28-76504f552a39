import{j as e}from"./createLucideIcon.CviQQVa0.js";import{C as r}from"./card.CpJhrToJ.js";import{C as l}from"./check.yToNqzhe.js";import"./index.RH_Wq4ov.js";import"./utils.CBfrqCZ4.js";function d(){return e.jsx("section",{className:"py-24 bg-white",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-5xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-gray-900 mb-4",children:"No Hidden Fees. Ever."}),e.jsx("p",{className:"text-xl text-gray-600",children:"See exactly what you'll pay before you book."})]}),e.jsxs(r,{className:"max-w-2xl mx-auto p-8 border-gray-200",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Example: Interior Painting Project"}),e.jsx("p",{className:"text-gray-600",children:"1,000 square feet, 2 coats, quality paint"})]}),e.jsxs("div",{className:"space-y-3 pb-6 border-b border-gray-100",children:[e.jsx(t,{label:"Labor",value:"$850"}),e.jsx(t,{label:"Materials (Premium Paint)",value:"$400"}),e.jsx(t,{label:"Platform Fee (20%)",value:"$250"})]}),e.jsxs("div",{className:"flex justify-between items-center pt-6 pb-4",children:[e.jsx("span",{className:"text-lg font-semibold text-gray-900",children:"Your Total"}),e.jsx("span",{className:"text-3xl font-bold text-gray-900",children:"$1,500"})]}),e.jsxs("div",{className:"bg-gray-50 -mx-8 -mb-8 mt-6 p-6 rounded-b-lg",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-gray-600",children:"Traditional Quotes Range"}),e.jsx("span",{className:"text-gray-500 line-through",children:"$1,800-2,400"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"font-semibold text-gray-900",children:"Your Savings"}),e.jsx("span",{className:"font-semibold text-green-600",children:"$300-900"})]})]})]}),e.jsxs("div",{className:"mt-16 grid md:grid-cols-2 gap-12 max-w-4xl mx-auto",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Always Included"}),e.jsx("ul",{className:"space-y-3",children:["All labor costs","Standard materials","Insurance coverage","Quality guarantee","Photo documentation","Tax-ready invoicing"].map((s,a)=>e.jsxs("li",{className:"flex items-start gap-3",children:[e.jsx(l,{className:"w-5 h-5 text-green-600 mt-0.5 flex-shrink-0"}),e.jsx("span",{className:"text-gray-700",children:s})]},a))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Never Charged For"}),e.jsx("ul",{className:"space-y-3",children:["Quotes or estimates","Scheduling changes","Payment processing","Contractor matching","Basic documentation","Customer support"].map((s,a)=>e.jsxs("li",{className:"flex items-start gap-3",children:[e.jsx(l,{className:"w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0"}),e.jsx("span",{className:"text-gray-700",children:s})]},a))})]})]})]})})})}function t({label:s,value:a}){return e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:s}),e.jsx("span",{className:"font-medium text-gray-900",children:a})]})}export{d as default};
