const VIRTUAL_MODULE_ID = "astro:actions";
const RESOLVED_VIRTUAL_MODULE_ID = "\0" + VIRTUAL_MODULE_ID;
const ACTIONS_TYPES_FILE = "actions.d.ts";
const ASTRO_ACTIONS_INTERNAL_MODULE_ID = "astro-internal:actions";
const RESOLVED_ASTRO_ACTIONS_INTERNAL_MODULE_ID = "\0" + ASTRO_ACTIONS_INTERNAL_MODULE_ID;
const NOOP_ACTIONS = "\0noop-actions";
const ACTION_QUERY_PARAMS = {
  actionName: "_action",
  actionPayload: "_astroActionPayload"
};
const ACTION_RPC_ROUTE_PATTERN = "/_actions/[...path]";
export {
  ACTIONS_TYPES_FILE,
  ACTION_QUERY_PARAMS,
  ACTION_RPC_ROUTE_PATTERN,
  ASTRO_ACTIONS_INTERNAL_MODULE_ID,
  NOOP_ACTIONS,
  RESOLVED_ASTRO_ACTIONS_INTERNAL_MODULE_ID,
  RESOLVED_VIRTUAL_MODULE_ID,
  VIRTUAL_MODULE_ID
};
