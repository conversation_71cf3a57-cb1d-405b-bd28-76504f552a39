# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

Clear Quote Pro is a platform connecting property owners with pre-vetted contractors for home maintenance and repairs. It's built as a Turborepo monorepo using pnpm workspaces.

## Development Commands

### Package Management
- **Package Manager**: pnpm (v10.14.0)
- **Install dependencies**: `pnpm install`

### Common Commands
```bash
# Development
pnpm dev                  # Start all apps in development mode
pnpm dev --filter=landing # Develop specific app (e.g., landing)

# Build
pnpm build                # Build all apps and packages
pnpm build --filter=docs  # Build specific app

# Code Quality
pnpm lint                 # Run linting across all packages
pnpm format              # Format all TypeScript and Markdown files
pnpm check-types         # Type check all packages

# Turbo-specific
turbo dev --filter=landing  # Using global turbo installation
npx turbo dev --filter=web  # Without global turbo
```

## Architecture

### Monorepo Structure
```
apps/
├── landing/          # Astro 5 marketing website (React 19 components)
└── [future apps]     # Homeowner app, vendor dashboard, admin dashboard

packages/
├── ui/              # Shared React component library (shadcn/ui based)
├── eslint-config/   # Shared ESLint configurations
└── typescript-config/ # Shared TypeScript configurations
```

### Tech Stack
- **Landing Page**: Astro 5 with React 19 integration, Tailwind CSS v4
- **UI Components**: shadcn/ui, Radix UI primitives, class-variance-authority
- **Build System**: Turborepo with dependency-aware builds
- **Type Safety**: TypeScript 5.9.2 across all packages

### Key Architectural Decisions

1. **Turborepo Pipeline**: Tasks are configured with dependencies (`^build`) ensuring packages build before apps that consume them.

2. **Shared UI Package**: The `@repo/ui` package exports reusable components used across all apps, maintaining consistency.

3. **Landing Page Isolation**: The landing app is self-contained with its own component structure while still leveraging shared packages.

## Development Workflow

### Working with the Landing Page
The landing page (`apps/landing`) uses Astro with MDX support and React components:
- Development server: `pnpm dev --filter=landing`
- Pages are in `src/pages/` (file-based routing)
- React components in `src/components/`
- Global styles in `src/styles/global.css`

### Adding New Apps
When adding new apps (homeowner, vendor, admin):
1. Create in `apps/` directory
2. Add to pnpm workspace
3. Configure in turbo.json if needed
4. Use shared packages (`@repo/ui`, configs)

### Type Checking
Run `pnpm check-types` before committing to ensure type safety across the monorepo.

## Project Context

### Business Goals
- Connect property owners with verified contractors
- Transparent, pre-negotiated pricing
- Streamlined project management
- Multi-property portfolio management

### Planned Applications
1. **Landing Page** (Current): Marketing and early access signup
2. **Homeowner App**: Service requests, project tracking, payments
3. **Vendor Dashboard**: Job management, scheduling, earnings
4. **Admin Dashboard**: Platform management, analytics, support

### Documentation
Comprehensive planning documentation is available in the `/plan` directory covering:
- System architecture and business model
- Individual app specifications
- Database design
- API patterns
- Component library guidelines