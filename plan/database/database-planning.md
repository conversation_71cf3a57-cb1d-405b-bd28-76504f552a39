# Database Planning - Clear Quote Pro

## Overview

Clear Quote Pro uses a single PostgreSQL database to manage all platform data across the landing page, homeowner app, vendor dashboard, and admin dashboard. The database is designed for scalability, data integrity, and efficient querying with proper indexing and relationships.

## Database Architecture

### Environment Setup
- **Development**: Local PostgreSQL 15+
- **Production**: Neon PostgreSQL on Azure
- **ORM**: Drizzle ORM for type-safe database access
- **Migrations**: Drizzle Kit for schema migrations
- **Connection Pooling**: PgBouncer (production)

## Core Design Principles

### 1. Data Integrity
- Foreign key constraints for referential integrity
- Check constraints for business rules
- Unique constraints for preventing duplicates
- NOT NULL constraints where appropriate

### 2. Performance Optimization
- Strategic indexing on frequently queried columns
- Composite indexes for complex queries
- Partial indexes for filtered queries
- JSONB for flexible semi-structured data

### 3. Security
- Row-level security (RLS) for multi-tenancy
- Encrypted sensitive data (SSN, TIN, payment info)
- Audit logging for compliance
- Soft deletes for data recovery

### 4. Scalability
- Partitioning for large tables (audit logs, analytics)
- Efficient foreign key design
- Denormalization where appropriate
- Archive strategy for old data

## Database Schema

### User Management Domain

#### users (Core authentication table - managed by Better Auth)
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique user identifier |
| email | VARCHAR(255) | UNIQUE, NOT NULL | User email address |
| email_verified | BOOLEAN | DEFAULT FALSE | Email verification status |
| password_hash | TEXT | | Hashed password |
| created_at | TIMESTAMP | DEFAULT NOW() | Account creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

#### user_profiles (Extended user information)
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Profile identifier |
| user_id | UUID | FOREIGN KEY -> users.id, UNIQUE | Link to auth user |
| user_type | ENUM | NOT NULL | 'homeowner', 'landlord', 'vendor', 'admin' |
| first_name | VARCHAR(100) | NOT NULL | User's first name |
| last_name | VARCHAR(100) | NOT NULL | User's last name |
| phone | VARCHAR(20) | | Phone number |
| phone_verified | BOOLEAN | DEFAULT FALSE | Phone verification status |
| avatar_url | TEXT | | Profile picture URL |
| address | TEXT | | Street address |
| city | VARCHAR(100) | | City |
| state | VARCHAR(2) | | State code |
| zip | VARCHAR(10) | | ZIP code |
| profile_complete | BOOLEAN | DEFAULT FALSE | Profile completion status |
| stripe_customer_id | VARCHAR(100) | | Stripe customer ID |
| notification_preferences | JSONB | | Notification settings |
| created_at | TIMESTAMP | DEFAULT NOW() | Profile creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

### Property Management Domain

#### properties
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Property identifier |
| user_id | UUID | FOREIGN KEY -> users.id | Property owner |
| nickname | VARCHAR(100) | | Property nickname |
| address | TEXT | NOT NULL | Street address |
| city | VARCHAR(100) | NOT NULL | City |
| state | VARCHAR(2) | NOT NULL | State code |
| zip | VARCHAR(10) | NOT NULL | ZIP code |
| property_type | ENUM | NOT NULL | 'single_family', 'condo', 'townhouse', 'multi_family' |
| year_built | INTEGER | | Year of construction |
| sqft | INTEGER | | Square footage |
| bedrooms | INTEGER | | Number of bedrooms |
| bathrooms | DECIMAL(3,1) | | Number of bathrooms |
| is_primary | BOOLEAN | DEFAULT FALSE | Primary residence flag |
| is_rental | BOOLEAN | DEFAULT FALSE | Rental property flag |
| tenant_info | JSONB | | Tenant details |
| access_instructions | TEXT | | Entry instructions |
| photos | JSONB | | Property photos array |
| sibi_property_id | VARCHAR(100) | | Sibi Pro integration ID |
| metadata | JSONB | | Additional property data |
| deleted_at | TIMESTAMP | | Soft delete timestamp |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

### Vendor Management Domain

#### vendors
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Vendor identifier |
| user_id | UUID | FOREIGN KEY -> users.id, UNIQUE | Link to auth user |
| business_name | VARCHAR(255) | NOT NULL | Legal business name |
| dba | VARCHAR(255) | | Doing Business As name |
| business_type | ENUM | | 'sole_prop', 'llc', 'corp', 's_corp' |
| tin_encrypted | TEXT | | Encrypted Tax ID |
| business_address | TEXT | NOT NULL | Business address |
| business_city | VARCHAR(100) | NOT NULL | Business city |
| business_state | VARCHAR(2) | NOT NULL | Business state |
| business_zip | VARCHAR(10) | NOT NULL | Business ZIP |
| business_phone | VARCHAR(20) | NOT NULL | Business phone |
| website | TEXT | | Company website |
| years_in_business | INTEGER | | Years operating |
| employee_count | INTEGER | | Number of employees |
| status | ENUM | NOT NULL | 'pending', 'active', 'suspended', 'inactive' |
| verified_at | TIMESTAMP | | Verification timestamp |
| verified_by | UUID | FOREIGN KEY -> admin_users.id | Verifying admin |
| suspended_at | TIMESTAMP | | Suspension timestamp |
| suspension_reason | TEXT | | Reason for suspension |
| service_radius | INTEGER | DEFAULT 25 | Service radius in miles |
| total_jobs_completed | INTEGER | DEFAULT 0 | Completed jobs count |
| total_earnings | DECIMAL(12,2) | DEFAULT 0 | Total earnings |
| average_rating | DECIMAL(3,2) | | Average customer rating |
| response_rate | DECIMAL(5,2) | | Job response percentage |
| on_time_rate | DECIMAL(5,2) | | On-time completion rate |
| metadata | JSONB | | Additional vendor data |
| created_at | TIMESTAMP | DEFAULT NOW() | Registration time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

#### vendor_services
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Service offering ID |
| vendor_id | UUID | FOREIGN KEY -> vendors.id | Vendor reference |
| service_id | UUID | FOREIGN KEY -> services.id | Service reference |
| sub_services | JSONB | | Specific sub-services |
| experience_years | INTEGER | | Years of experience |
| requires_license | BOOLEAN | DEFAULT FALSE | License requirement |
| license_number | VARCHAR(100) | | Professional license |
| license_expiry | DATE | | License expiration |
| license_verified | BOOLEAN | DEFAULT FALSE | License verification |
| custom_pricing | JSONB | | Custom price overrides |
| active | BOOLEAN | DEFAULT TRUE | Service active status |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |

#### vendor_documents
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Document identifier |
| vendor_id | UUID | FOREIGN KEY -> vendors.id | Vendor reference |
| document_type | ENUM | NOT NULL | 'coi', 'license', 'w9', 'background' |
| document_name | VARCHAR(255) | NOT NULL | Document name |
| file_url | TEXT | NOT NULL | R2 storage URL |
| file_size | INTEGER | | File size in bytes |
| issue_date | DATE | | Document issue date |
| expiry_date | DATE | | Document expiration |
| document_number | VARCHAR(100) | | Document number |
| status | ENUM | NOT NULL | 'pending', 'verified', 'expired', 'rejected' |
| verified_by | UUID | FOREIGN KEY -> admin_users.id | Verifying admin |
| verified_at | TIMESTAMP | | Verification timestamp |
| rejection_reason | TEXT | | Rejection explanation |
| reminder_sent_30 | BOOLEAN | DEFAULT FALSE | 30-day reminder sent |
| reminder_sent_60 | BOOLEAN | DEFAULT FALSE | 60-day reminder sent |
| created_at | TIMESTAMP | DEFAULT NOW() | Upload time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

#### vendor_service_areas
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Service area ID |
| vendor_id | UUID | FOREIGN KEY -> vendors.id | Vendor reference |
| coverage_type | ENUM | NOT NULL | 'metro', 'county', 'city', 'zip' |
| metro_areas | JSONB | | Metro area list |
| counties | JSONB | | County list |
| cities | JSONB | | City list |
| zip_codes | JSONB | | ZIP code list |
| max_distance | INTEGER | | Maximum travel distance |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

#### vendor_team_members
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Team member ID |
| vendor_id | UUID | FOREIGN KEY -> vendors.id | Vendor reference |
| name | VARCHAR(255) | NOT NULL | Member name |
| email | VARCHAR(255) | NOT NULL | Member email |
| phone | VARCHAR(20) | | Member phone |
| role | ENUM | NOT NULL | 'owner', 'manager', 'crew_lead', 'worker' |
| permissions | JSONB | | Permission list |
| can_accept_jobs | BOOLEAN | DEFAULT FALSE | Job acceptance permission |
| can_update_progress | BOOLEAN | DEFAULT TRUE | Progress update permission |
| can_view_financials | BOOLEAN | DEFAULT FALSE | Financial view permission |
| invite_token | VARCHAR(100) | | Invitation token |
| invited_at | TIMESTAMP | | Invitation timestamp |
| accepted_at | TIMESTAMP | | Acceptance timestamp |
| user_id | UUID | FOREIGN KEY -> users.id | User account if created |
| active | BOOLEAN | DEFAULT TRUE | Active status |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |

### Service Configuration Domain

#### services
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Service identifier |
| name | VARCHAR(100) | NOT NULL | Service name |
| slug | VARCHAR(100) | UNIQUE, NOT NULL | URL-friendly name |
| category | VARCHAR(50) | NOT NULL | Service category |
| description | TEXT | | Service description |
| base_price | DECIMAL(10,2) | | Base price |
| price_unit | VARCHAR(20) | | 'sqft', 'hour', 'project' |
| requires_license | BOOLEAN | DEFAULT FALSE | License requirement |
| icon | VARCHAR(50) | | Icon identifier |
| active | BOOLEAN | DEFAULT TRUE | Service availability |
| metadata | JSONB | | Additional service data |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

#### service_pricing_rules
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Pricing rule ID |
| service_id | UUID | FOREIGN KEY -> services.id | Service reference |
| name | VARCHAR(100) | NOT NULL | Rule name |
| condition_type | VARCHAR(50) | | 'variant', 'region', 'season' |
| conditions | JSONB | NOT NULL | Rule conditions |
| price_adjustment | DECIMAL(10,2) | | Fixed adjustment |
| price_multiplier | DECIMAL(5,3) | | Percentage adjustment |
| priority | INTEGER | DEFAULT 0 | Rule priority |
| active | BOOLEAN | DEFAULT TRUE | Rule active status |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |

### Project Management Domain

#### projects
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Project identifier |
| user_id | UUID | FOREIGN KEY -> users.id | Homeowner reference |
| property_id | UUID | FOREIGN KEY -> properties.id | Property reference |
| service_id | UUID | FOREIGN KEY -> services.id | Service reference |
| vendor_id | UUID | FOREIGN KEY -> vendors.id | Selected vendor |
| status | ENUM | NOT NULL | 'draft', 'pending_vendors', 'vendor_selected', 'scheduled', 'in_progress', 'completed', 'cancelled' |
| title | VARCHAR(255) | NOT NULL | Project title |
| description | TEXT | | Project description |
| scope | JSONB | NOT NULL | Detailed scope |
| measurements | JSONB | | Room measurements |
| materials | JSONB | | Selected materials |
| photos_before | JSONB | | Before photos |
| photos_during | JSONB | | Progress photos |
| photos_after | JSONB | | Completion photos |
| labor_cost | DECIMAL(10,2) | | Labor cost |
| material_cost | DECIMAL(10,2) | | Material cost |
| platform_fee | DECIMAL(10,2) | | Platform fee (20%) |
| total_cost | DECIMAL(10,2) | NOT NULL | Total project cost |
| preferred_start_date | DATE | | Customer preference |
| scheduled_start_date | DATE | | Confirmed start |
| scheduled_end_date | DATE | | Expected completion |
| actual_start_date | DATE | | Actual start |
| actual_end_date | DATE | | Actual completion |
| cancellation_reason | TEXT | | If cancelled |
| cancelled_by | UUID | FOREIGN KEY -> users.id | Who cancelled |
| cancelled_at | TIMESTAMP | | Cancellation time |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

#### project_vendors
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Response identifier |
| project_id | UUID | FOREIGN KEY -> projects.id | Project reference |
| vendor_id | UUID | FOREIGN KEY -> vendors.id | Vendor reference |
| status | ENUM | NOT NULL | 'pending', 'accepted', 'declined', 'selected' |
| available_slots | JSONB | | Available time slots |
| proposed_start_date | DATE | | Vendor's proposed start |
| estimated_duration | INTEGER | | Hours to complete |
| crew_size | INTEGER | | Number of workers |
| message | TEXT | | Vendor message |
| responded_at | TIMESTAMP | | Response timestamp |
| selected_at | TIMESTAMP | | Selection timestamp |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |

#### project_milestones
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Milestone identifier |
| project_id | UUID | FOREIGN KEY -> projects.id | Project reference |
| name | VARCHAR(100) | NOT NULL | Milestone name |
| description | TEXT | | Milestone description |
| sequence | INTEGER | NOT NULL | Order sequence |
| status | ENUM | NOT NULL | 'pending', 'in_progress', 'completed' |
| completed_at | TIMESTAMP | | Completion timestamp |
| photos | JSONB | | Milestone photos |
| notes | TEXT | | Progress notes |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |

### Financial Domain

#### payments
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Payment identifier |
| project_id | UUID | FOREIGN KEY -> projects.id | Project reference |
| user_id | UUID | FOREIGN KEY -> users.id | Payer reference |
| vendor_id | UUID | FOREIGN KEY -> vendors.id | Payee reference |
| amount | DECIMAL(10,2) | NOT NULL | Payment amount |
| type | ENUM | NOT NULL | 'deposit', 'progress', 'final', 'refund' |
| milestone | VARCHAR(50) | | Payment milestone |
| status | ENUM | NOT NULL | 'pending', 'processing', 'completed', 'failed', 'refunded' |
| payment_method | VARCHAR(50) | | 'card', 'ach', 'affirm' |
| payment_intent_id | VARCHAR(100) | | Stripe payment intent |
| transaction_id | VARCHAR(100) | | Transaction reference |
| receipt_url | TEXT | | Receipt URL |
| failure_reason | TEXT | | If failed |
| refund_amount | DECIMAL(10,2) | | Refund amount |
| refunded_at | TIMESTAMP | | Refund timestamp |
| paid_at | TIMESTAMP | | Payment timestamp |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |

#### vendor_payouts
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Payout identifier |
| vendor_id | UUID | FOREIGN KEY -> vendors.id | Vendor reference |
| amount | DECIMAL(10,2) | NOT NULL | Payout amount |
| method | ENUM | NOT NULL | 'ach', 'wire', 'check' |
| status | ENUM | NOT NULL | 'pending', 'processing', 'completed', 'failed' |
| transaction_id | VARCHAR(100) | | Bank transaction ID |
| reference_number | VARCHAR(100) | | Reference number |
| project_ids | JSONB | | Related projects |
| initiated_by | UUID | FOREIGN KEY -> admin_users.id | Initiating admin |
| initiated_at | TIMESTAMP | DEFAULT NOW() | Initiation time |
| processed_at | TIMESTAMP | | Processing time |
| completed_at | TIMESTAMP | | Completion time |
| failure_reason | TEXT | | If failed |
| retry_count | INTEGER | DEFAULT 0 | Retry attempts |

#### vendor_payment_settings
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Settings identifier |
| vendor_id | UUID | FOREIGN KEY -> vendors.id, UNIQUE | Vendor reference |
| preferred_method | ENUM | NOT NULL | 'ach', 'wire', 'check' |
| ach_routing_encrypted | TEXT | | Encrypted routing |
| ach_account_encrypted | TEXT | | Encrypted account |
| ach_account_type | ENUM | | 'checking', 'savings' |
| wire_bank_name | VARCHAR(255) | | Wire bank name |
| wire_routing | VARCHAR(20) | | Wire routing |
| check_address | TEXT | | Check mailing address |
| tax_id_encrypted | TEXT | | Encrypted tax ID |
| w9_uploaded | BOOLEAN | DEFAULT FALSE | W9 status |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

### Communication Domain

#### messages
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Message identifier |
| project_id | UUID | FOREIGN KEY -> projects.id | Project context |
| sender_id | UUID | FOREIGN KEY -> users.id | Sender reference |
| sender_type | ENUM | NOT NULL | 'customer', 'vendor', 'admin', 'system' |
| recipient_id | UUID | FOREIGN KEY -> users.id | Recipient reference |
| content | TEXT | NOT NULL | Message content |
| attachments | JSONB | | File attachments |
| read_at | TIMESTAMP | | Read timestamp |
| deleted_at | TIMESTAMP | | Soft delete |
| created_at | TIMESTAMP | DEFAULT NOW() | Send time |

#### notifications
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Notification ID |
| user_id | UUID | FOREIGN KEY -> users.id | Recipient |
| type | VARCHAR(50) | NOT NULL | Notification type |
| title | VARCHAR(255) | NOT NULL | Notification title |
| content | TEXT | | Notification body |
| data | JSONB | | Additional data |
| channels | JSONB | | Delivery channels |
| read_at | TIMESTAMP | | Read timestamp |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |

### Review & Rating Domain

#### reviews
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Review identifier |
| project_id | UUID | FOREIGN KEY -> projects.id, UNIQUE | Project reference |
| reviewer_id | UUID | FOREIGN KEY -> users.id | Reviewer |
| reviewer_type | ENUM | NOT NULL | 'customer', 'vendor' |
| reviewee_id | UUID | FOREIGN KEY -> users.id | Review subject |
| overall_rating | INTEGER | NOT NULL CHECK (1-5) | Overall rating |
| quality_rating | INTEGER | CHECK (1-5) | Quality rating |
| timeliness_rating | INTEGER | CHECK (1-5) | Timeliness rating |
| communication_rating | INTEGER | CHECK (1-5) | Communication rating |
| cleanliness_rating | INTEGER | CHECK (1-5) | Cleanliness rating |
| comment | TEXT | | Review text |
| photos | JSONB | | Review photos |
| response | TEXT | | Vendor response |
| responded_at | TIMESTAMP | | Response time |
| helpful_count | INTEGER | DEFAULT 0 | Helpful votes |
| created_at | TIMESTAMP | DEFAULT NOW() | Review time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update |

### Dispute Management Domain

#### disputes
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Dispute identifier |
| project_id | UUID | FOREIGN KEY -> projects.id | Project reference |
| customer_id | UUID | FOREIGN KEY -> users.id | Customer |
| vendor_id | UUID | FOREIGN KEY -> vendors.id | Vendor |
| status | ENUM | NOT NULL | 'open', 'under_review', 'resolved', 'escalated', 'closed' |
| priority | ENUM | NOT NULL | 'low', 'medium', 'high', 'urgent' |
| category | ENUM | NOT NULL | 'quality', 'payment', 'schedule', 'communication', 'other' |
| customer_statement | TEXT | NOT NULL | Customer complaint |
| vendor_response | TEXT | | Vendor response |
| disputed_amount | DECIMAL(10,2) | | Disputed amount |
| refund_amount | DECIMAL(10,2) | | Refund if applicable |
| assigned_admin_id | UUID | FOREIGN KEY -> admin_users.id | Assigned admin |
| assigned_at | TIMESTAMP | | Assignment time |
| resolution | TEXT | | Resolution description |
| resolution_notes | TEXT | | Admin notes |
| resolved_by | UUID | FOREIGN KEY -> admin_users.id | Resolving admin |
| resolved_at | TIMESTAMP | | Resolution time |
| evidence | JSONB | | Supporting evidence |
| created_at | TIMESTAMP | DEFAULT NOW() | Dispute opened |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update |

### Admin & Platform Domain

#### admin_users
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Admin identifier |
| user_id | UUID | FOREIGN KEY -> users.id, UNIQUE | User account |
| role | ENUM | NOT NULL | 'super_admin', 'admin', 'support', 'analyst' |
| permissions | JSONB | | Custom permissions |
| last_login_at | TIMESTAMP | | Last login time |
| last_activity_at | TIMESTAMP | | Last activity |
| notification_preferences | JSONB | | Notification settings |
| dashboard_layout | JSONB | | UI preferences |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update |

#### audit_log
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Log entry ID |
| admin_id | UUID | FOREIGN KEY -> admin_users.id | Acting admin |
| action_type | VARCHAR(50) | NOT NULL | Action type |
| entity_type | VARCHAR(50) | NOT NULL | Entity type |
| entity_id | VARCHAR(100) | NOT NULL | Entity ID |
| description | TEXT | NOT NULL | Action description |
| previous_value | JSONB | | Before state |
| new_value | JSONB | | After state |
| metadata | JSONB | | Additional data |
| ip_address | INET | | Request IP |
| user_agent | TEXT | | Browser info |
| created_at | TIMESTAMP | DEFAULT NOW() | Action time |

#### platform_settings
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Setting ID |
| key | VARCHAR(100) | UNIQUE, NOT NULL | Setting key |
| value | JSONB | NOT NULL | Setting value |
| category | VARCHAR(50) | NOT NULL | Setting category |
| description | TEXT | | Setting description |
| updated_by | UUID | FOREIGN KEY -> admin_users.id | Last updater |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update |

#### support_tickets
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Ticket ID |
| user_id | UUID | FOREIGN KEY -> users.id | User |
| user_type | ENUM | NOT NULL | 'customer', 'vendor' |
| subject | VARCHAR(255) | NOT NULL | Ticket subject |
| description | TEXT | NOT NULL | Issue description |
| category | ENUM | NOT NULL | 'technical', 'billing', 'account', 'dispute' |
| priority | ENUM | NOT NULL | 'low', 'medium', 'high', 'urgent' |
| status | ENUM | NOT NULL | 'open', 'in_progress', 'waiting', 'resolved', 'closed' |
| assigned_to | UUID | FOREIGN KEY -> admin_users.id | Assigned admin |
| assigned_at | TIMESTAMP | | Assignment time |
| resolution | TEXT | | Resolution notes |
| resolved_at | TIMESTAMP | | Resolution time |
| closed_at | TIMESTAMP | | Closure time |
| first_response_at | TIMESTAMP | | First response |
| satisfaction_rating | INTEGER | CHECK (1-5) | Satisfaction |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update |

### Analytics Domain

#### analytics_events
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Event ID |
| user_id | UUID | FOREIGN KEY -> users.id | User |
| session_id | VARCHAR(100) | | Session ID |
| event_type | VARCHAR(50) | NOT NULL | Event type |
| event_name | VARCHAR(100) | NOT NULL | Event name |
| properties | JSONB | | Event properties |
| page_url | TEXT | | Page URL |
| referrer | TEXT | | Referrer URL |
| user_agent | TEXT | | Browser info |
| ip_address | INET | | IP address |
| created_at | TIMESTAMP | DEFAULT NOW() | Event time |

#### analytics_snapshots
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Snapshot ID |
| date | DATE | NOT NULL | Snapshot date |
| period | ENUM | NOT NULL | 'daily', 'weekly', 'monthly' |
| total_users | INTEGER | | Total users |
| new_users | INTEGER | | New users |
| active_users | INTEGER | | Active users |
| total_vendors | INTEGER | | Total vendors |
| active_vendors | INTEGER | | Active vendors |
| total_jobs | INTEGER | | Total jobs |
| completed_jobs | INTEGER | | Completed jobs |
| average_job_value | DECIMAL(10,2) | | Avg job value |
| total_revenue | DECIMAL(12,2) | | Total revenue |
| platform_fees | DECIMAL(12,2) | | Platform fees |
| average_rating | DECIMAL(3,2) | | Avg rating |
| dispute_rate | DECIMAL(5,2) | | Dispute rate |
| completion_rate | DECIMAL(5,2) | | Completion rate |
| created_at | TIMESTAMP | DEFAULT NOW() | Creation time |

## Indexes

### Performance Indexes
```sql
-- User queries
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);

-- Property queries
CREATE INDEX idx_properties_user_id ON properties(user_id);
CREATE INDEX idx_properties_zip ON properties(zip);

-- Vendor queries
CREATE INDEX idx_vendors_status ON vendors(status);
CREATE INDEX idx_vendors_user_id ON vendors(user_id);
CREATE INDEX idx_vendor_services_vendor_id ON vendor_services(vendor_id);
CREATE INDEX idx_vendor_documents_vendor_id_status ON vendor_documents(vendor_id, status);

-- Project queries
CREATE INDEX idx_projects_user_id_status ON projects(user_id, status);
CREATE INDEX idx_projects_vendor_id_status ON projects(vendor_id, status);
CREATE INDEX idx_projects_property_id ON projects(property_id);
CREATE INDEX idx_project_vendors_project_id ON project_vendors(project_id);

-- Payment queries
CREATE INDEX idx_payments_project_id ON payments(project_id);
CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_vendor_payouts_vendor_id_status ON vendor_payouts(vendor_id, status);

-- Message queries
CREATE INDEX idx_messages_project_id ON messages(project_id);
CREATE INDEX idx_messages_sender_recipient ON messages(sender_id, recipient_id);

-- Notification queries
CREATE INDEX idx_notifications_user_id_read ON notifications(user_id, read_at);

-- Review queries
CREATE INDEX idx_reviews_reviewee_id ON reviews(reviewee_id);
CREATE INDEX idx_reviews_project_id ON reviews(project_id);

-- Dispute queries
CREATE INDEX idx_disputes_status_priority ON disputes(status, priority);

-- Audit queries
CREATE INDEX idx_audit_log_entity ON audit_log(entity_type, entity_id);
CREATE INDEX idx_audit_log_admin_date ON audit_log(admin_id, created_at);
```

## Database Views

### Vendor Dashboard View
```sql
CREATE VIEW vendor_dashboard AS
SELECT 
    v.id,
    v.business_name,
    v.status,
    v.average_rating,
    COUNT(DISTINCT p.id) as total_jobs,
    COUNT(DISTINCT CASE WHEN p.status = 'completed' THEN p.id END) as completed_jobs,
    COUNT(DISTINCT CASE WHEN p.status = 'in_progress' THEN p.id END) as active_jobs,
    SUM(CASE WHEN p.status = 'completed' THEN p.total_cost * 0.8 END) as total_earnings
FROM vendors v
LEFT JOIN projects p ON p.vendor_id = v.id
GROUP BY v.id;
```

### Customer Project Summary View
```sql
CREATE VIEW customer_project_summary AS
SELECT 
    u.id as user_id,
    COUNT(DISTINCT p.id) as total_projects,
    COUNT(DISTINCT CASE WHEN p.status = 'completed' THEN p.id END) as completed_projects,
    SUM(p.total_cost) as total_spent,
    AVG(r.overall_rating) as avg_rating_given
FROM users u
LEFT JOIN projects p ON p.user_id = u.id
LEFT JOIN reviews r ON r.reviewer_id = u.id AND r.reviewer_type = 'customer'
GROUP BY u.id;
```

## Database Functions

### Calculate Platform Fee
```sql
CREATE OR REPLACE FUNCTION calculate_platform_fee(
    labor_cost DECIMAL,
    material_cost DECIMAL
) RETURNS DECIMAL AS $$
BEGIN
    RETURN (labor_cost + material_cost) * 0.20;
END;
$$ LANGUAGE plpgsql;
```

### Update Vendor Metrics
```sql
CREATE OR REPLACE FUNCTION update_vendor_metrics() RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        UPDATE vendors 
        SET 
            total_jobs_completed = total_jobs_completed + 1,
            total_earnings = total_earnings + (NEW.total_cost * 0.8)
        WHERE id = NEW.vendor_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_vendor_metrics_trigger
AFTER UPDATE ON projects
FOR EACH ROW
EXECUTE FUNCTION update_vendor_metrics();
```

## Data Migration Strategy

### Development to Production
1. Schema migration using Drizzle Kit
2. Seed data for services and pricing
3. Test data cleanup
4. Production environment variables

### Backup Strategy
1. Daily automated backups (Neon)
2. Point-in-time recovery (7 days)
3. Cross-region replication
4. Monthly archive exports

## Security Considerations

### Data Encryption
- Sensitive fields encrypted at rest (TIN, SSN, payment info)
- TLS for data in transit
- Encryption keys in Azure Key Vault

### Access Control
- Row-level security for multi-tenancy
- Role-based access via application layer
- Database user permissions per environment

### Compliance
- PCI DSS for payment data
- GDPR compliance for user data
- Audit logging for all admin actions
- Data retention policies

## Performance Optimization

### Query Optimization
- Explain analyze for slow queries
- Query plan caching
- Connection pooling (PgBouncer)
- Read replicas for analytics

### Maintenance
- Weekly VACUUM ANALYZE
- Index rebuilding monthly
- Partition old audit logs
- Archive completed projects > 2 years

## Monitoring

### Key Metrics
- Query performance (> 100ms)
- Connection pool usage
- Table bloat
- Index usage
- Lock waits
- Replication lag

### Alerts
- Failed migrations
- Connection exhaustion
- Slow query threshold
- Disk space usage > 80%
- Replication lag > 1 minute