#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/esbuild@0.25.8/node_modules/esbuild/bin/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/esbuild@0.25.8/node_modules/esbuild/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/esbuild@0.25.8/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/esbuild@0.25.8/node_modules/esbuild/bin/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/esbuild@0.25.8/node_modules/esbuild/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/esbuild@0.25.8/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
"$basedir/../../../esbuild/bin/esbuild"   "$@"
exit $?
