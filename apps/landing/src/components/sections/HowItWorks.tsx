import { Search, Calendar, CheckCircle } from 'lucide-react';

const steps = [
  {
    number: "01",
    title: "Describe Your Project",
    description: "Select the service, enter basic details like room size or scope. See your price instantly.",
    icon: Search
  },
  {
    number: "02", 
    title: "Book Your Professional",
    description: "Choose from available vetted contractors. Pick a time that works. Pay 50% to secure.",
    icon: Calendar
  },
  {
    number: "03",
    title: "Get It Done Right",
    description: "Track progress in real-time. Approve the work. Pay the balance. Rate your experience.",
    icon: CheckCircle
  }
];

export default function HowItWorks() {
  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto">
          {/* Section header */}
          <div className="text-center mb-16">
            <h2 className="text-gray-900 mb-4">
              Three Steps to Done
            </h2>
            <p className="text-xl text-gray-600">
              From request to completion in days, not weeks.
            </p>
          </div>
          
          {/* Steps */}
          <div className="space-y-8 md:space-y-0 md:grid md:grid-cols-3 md:gap-8">
            {steps.map((step, index) => {
              const Icon = step.icon;
              return (
                <div key={index} className="relative">
                  {/* Connector line (hidden on mobile) */}
                  {index < steps.length - 1 && (
                    <div className="hidden md:block absolute top-12 left-[60%] right-[-40%] h-[1px] bg-gradient-to-r from-gray-300 to-transparent" />
                  )}
                  
                  <div className="text-center md:text-left">
                    {/* Step number */}
                    <div className="inline-flex items-center justify-center w-12 h-12 mb-4">
                      <span className="text-4xl font-bold text-gray-200">
                        {step.number}
                      </span>
                    </div>
                    
                    {/* Icon */}
                    <div className="mb-4">
                      <Icon className="w-8 h-8 text-gray-900 mx-auto md:mx-0" />
                    </div>
                    
                    {/* Content */}
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {step.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {step.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
          
          {/* Bottom CTA */}
          <div className="mt-16 p-8 bg-gray-50 rounded-2xl text-center">
            <p className="text-lg text-gray-600 mb-2">
              Ready to skip the hassle?
            </p>
            <p className="text-2xl font-semibold text-gray-900">
              Your first project ships with a 60-day guarantee.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}