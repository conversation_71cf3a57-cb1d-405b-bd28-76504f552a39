#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/astro@5.12.8_@types+node@24.2.0_jiti@2.5.1_lightningcss@1.30.1_rollup@4.46.2_typescript@5.9.2/node_modules/astro/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/astro@5.12.8_@types+node@24.2.0_jiti@2.5.1_lightningcss@1.30.1_rollup@4.46.2_typescript@5.9.2/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/astro@5.12.8_@types+node@24.2.0_jiti@2.5.1_lightningcss@1.30.1_rollup@4.46.2_typescript@5.9.2/node_modules/astro/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/astro@5.12.8_@types+node@24.2.0_jiti@2.5.1_lightningcss@1.30.1_rollup@4.46.2_typescript@5.9.2/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../astro/astro.js" "$@"
else
  exec node  "$basedir/../astro/astro.js" "$@"
fi
