# Vendor Dashboard Development - Clear Quote Pro

## Tech Stack

### Core Technologies
- **Framework**: Astro 5.x (Hybrid rendering)
- **UI Framework**: React 19.x (Islands architecture)
- **Styling**: Tailwind CSS 4.x
- **Component Library**: shadcn/ui (shared package)
- **State Management**: Zustand
- **Forms**: React Hook Form + Zod
- **Animations**: anime.js 4.x
- **Database ORM**: Drizzle
- **Authentication**: Better Auth
- **File Storage**: Cloudflare R2
- **Real-time**: Server-Sent Events (SSE)
- **Email**: Resend
- **SMS**: <PERSON><PERSON><PERSON> (mocked initially)
- **Analytics**: Simple Analytics
- **Monitoring**: Sentry
- **Deployment**: Vercel

## Project Structure

```
apps/vendor-dashboard/
├── src/
│   ├── components/
│   │   ├── onboarding/
│   │   │   ├── OnboardingWizard.tsx
│   │   │   ├── BusinessInfoForm.tsx
│   │   │   ├── InsuranceUpload.tsx
│   │   │   ├── LicenseVerification.tsx
│   │   │   ├── ServiceSelection.tsx
│   │   │   ├── ServiceAreaConfig.tsx
│   │   │   ├── FinancialSetup.tsx
│   │   │   └── TeamSetup.tsx
│   │   ├── dashboard/
│   │   │   ├── DashboardLayout.tsx
│   │   │   ├── StatsOverview.tsx
│   │   │   ├── JobOpportunities.tsx
│   │   │   ├── TodaySchedule.tsx
│   │   │   ├── RevenueChart.tsx
│   │   │   └── QuickActions.tsx
│   │   ├── jobs/
│   │   │   ├── JobList.tsx
│   │   │   ├── JobCard.tsx
│   │   │   ├── JobDetails.tsx
│   │   │   ├── JobAcceptance.tsx
│   │   │   ├── JobProgress.tsx
│   │   │   ├── JobTimeline.tsx
│   │   │   └── JobFilters.tsx
│   │   ├── scheduling/
│   │   │   ├── CalendarView.tsx
│   │   │   ├── AvailabilityManager.tsx
│   │   │   ├── CrewScheduler.tsx
│   │   │   └── JobScheduler.tsx
│   │   ├── financial/
│   │   │   ├── EarningsOverview.tsx
│   │   │   ├── PaymentHistory.tsx
│   │   │   ├── InvoiceGenerator.tsx
│   │   │   ├── TaxDocuments.tsx
│   │   │   └── PayoutSettings.tsx
│   │   ├── compliance/
│   │   │   ├── ComplianceCenter.tsx
│   │   │   ├── DocumentManager.tsx
│   │   │   ├── LicenseTracker.tsx
│   │   │   ├── InsuranceMonitor.tsx
│   │   │   └── ExpirationAlerts.tsx
│   │   ├── team/
│   │   │   ├── TeamList.tsx
│   │   │   ├── MemberInvite.tsx
│   │   │   ├── RoleManager.tsx
│   │   │   └── CrewAssignment.tsx
│   │   ├── communication/
│   │   │   ├── MessageCenter.tsx
│   │   │   ├── JobChat.tsx
│   │   │   ├── NotificationFeed.tsx
│   │   │   └── AnnouncementBanner.tsx
│   │   ├── reviews/
│   │   │   ├── ReviewDashboard.tsx
│   │   │   ├── ReviewResponse.tsx
│   │   │   ├── RatingBreakdown.tsx
│   │   │   └── CustomerFeedback.tsx
│   │   └── shared/
│   │       ├── Navigation.tsx
│   │       ├── MobileNav.tsx
│   │       ├── NotificationBell.tsx
│   │       ├── StatusBadge.tsx
│   │       ├── PhotoUpload.tsx
│   │       └── DocumentViewer.tsx
│   ├── layouts/
│   │   ├── BaseLayout.astro
│   │   ├── DashboardLayout.astro
│   │   ├── OnboardingLayout.astro
│   │   └── MobileLayout.astro
│   ├── pages/
│   │   ├── index.astro                    # Redirect to dashboard
│   │   ├── auth/
│   │   │   ├── login.astro
│   │   │   ├── signup.astro
│   │   │   ├── verify.astro
│   │   │   └── forgot-password.astro
│   │   ├── onboarding/
│   │   │   ├── index.astro
│   │   │   ├── business.astro
│   │   │   ├── insurance.astro
│   │   │   ├── services.astro
│   │   │   ├── coverage.astro
│   │   │   ├── financial.astro
│   │   │   └── complete.astro
│   │   ├── dashboard.astro
│   │   ├── jobs/
│   │   │   ├── available.astro
│   │   │   ├── active.astro
│   │   │   ├── completed.astro
│   │   │   └── [id]/
│   │   │       ├── index.astro
│   │   │       ├── accept.astro
│   │   │       ├── progress.astro
│   │   │       └── complete.astro
│   │   ├── schedule/
│   │   │   ├── index.astro
│   │   │   ├── availability.astro
│   │   │   └── crew.astro
│   │   ├── financial/
│   │   │   ├── index.astro
│   │   │   ├── payments.astro
│   │   │   ├── invoices.astro
│   │   │   └── tax-docs.astro
│   │   ├── compliance/
│   │   │   ├── index.astro
│   │   │   ├── documents.astro
│   │   │   └── renewals.astro
│   │   ├── team/
│   │   │   ├── index.astro
│   │   │   ├── invite.astro
│   │   │   └── [memberId].astro
│   │   ├── messages/
│   │   │   ├── index.astro
│   │   │   └── [threadId].astro
│   │   ├── reviews/
│   │   │   ├── index.astro
│   │   │   └── respond.astro
│   │   ├── settings/
│   │   │   ├── index.astro
│   │   │   ├── profile.astro
│   │   │   ├── notifications.astro
│   │   │   └── api-keys.astro
│   │   └── api/
│   │       ├── auth/
│   │       │   └── [...all].ts
│   │       ├── vendor/
│   │       │   ├── onboarding.ts
│   │       │   ├── profile.ts
│   │       │   └── verification.ts
│   │       ├── jobs/
│   │       │   ├── available.ts
│   │       │   ├── accept.ts
│   │       │   ├── decline.ts
│   │       │   ├── progress.ts
│   │       │   └── complete.ts
│   │       ├── schedule/
│   │       │   ├── availability.ts
│   │       │   └── conflicts.ts
│   │       ├── financial/
│   │       │   ├── earnings.ts
│   │       │   ├── payments.ts
│   │       │   └── invoices.ts
│   │       ├── compliance/
│   │       │   ├── documents.ts
│   │       │   └── verify.ts
│   │       ├── team/
│   │       │   ├── members.ts
│   │       │   └── invite.ts
│   │       ├── notifications/
│   │       │   ├── subscribe.ts
│   │       │   └── preferences.ts
│   │       └── uploads/
│   │           └── presigned-url.ts
│   ├── lib/
│   │   ├── auth.ts
│   │   ├── db.ts
│   │   ├── r2.ts
│   │   ├── email.ts
│   │   ├── sms.ts
│   │   ├── sse.ts
│   │   ├── notifications.ts
│   │   ├── verification.ts
│   │   ├── constants.ts
│   │   └── utils.ts
│   ├── stores/
│   │   ├── vendor.store.ts
│   │   ├── jobs.store.ts
│   │   ├── schedule.store.ts
│   │   ├── financial.store.ts
│   │   └── notifications.store.ts
│   ├── hooks/
│   │   ├── useVendor.ts
│   │   ├── useJobs.ts
│   │   ├── useNotifications.ts
│   │   ├── useDocumentUpload.ts
│   │   └── useRealtime.ts
│   ├── services/
│   │   ├── job-matching.ts
│   │   ├── document-verification.ts
│   │   ├── payment-processing.ts
│   │   └── notification-service.ts
│   ├── middleware/
│   │   └── index.ts
│   └── styles/
│       └── global.css
├── public/
│   ├── images/
│   └── icons/
├── astro.config.mjs
├── tailwind.config.ts
├── tsconfig.json
└── package.json
```

## Database Schema

```typescript
// packages/database/src/schema/vendor.ts

import { 
  pgTable, 
  text, 
  timestamp, 
  uuid, 
  boolean, 
  integer, 
  decimal,
  jsonb,
  pgEnum 
} from 'drizzle-orm/pg-core'

// Enums
export const vendorStatusEnum = pgEnum('vendor_status', [
  'pending_verification',
  'active',
  'suspended',
  'inactive'
])

export const documentTypeEnum = pgEnum('document_type', [
  'insurance_coi',
  'business_license',
  'trade_license',
  'w9_tax',
  'background_check',
  'other'
])

export const documentStatusEnum = pgEnum('document_status', [
  'pending',
  'verified',
  'expired',
  'rejected'
])

export const jobResponseEnum = pgEnum('job_response', [
  'pending',
  'accepted',
  'declined',
  'expired'
])

// Vendor profiles
export const vendors = pgTable('vendors', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: text('user_id').notNull().unique(), // From Better Auth
  
  // Business Information
  businessName: text('business_name').notNull(),
  dba: text('dba'),
  businessType: text('business_type'), // 'sole_prop', 'llc', 'corp'
  businessAddress: text('business_address').notNull(),
  businessCity: text('business_city').notNull(),
  businessState: text('business_state').notNull(),
  businessZip: text('business_zip').notNull(),
  
  // Contact Information
  primaryPhone: text('primary_phone').notNull(),
  secondaryPhone: text('secondary_phone'),
  businessEmail: text('business_email').notNull(),
  website: text('website'),
  
  // Business Details
  yearsInBusiness: integer('years_in_business'),
  employeeCount: integer('employee_count'),
  tin: text('tin'), // Encrypted
  
  // Status
  status: vendorStatusEnum('status').notNull().default('pending_verification'),
  verifiedAt: timestamp('verified_at'),
  suspendedAt: timestamp('suspended_at'),
  suspensionReason: text('suspension_reason'),
  
  // Metrics
  totalJobsCompleted: integer('total_jobs_completed').default(0),
  averageRating: decimal('average_rating', { precision: 3, scale: 2 }),
  responseRate: decimal('response_rate', { precision: 5, scale: 2 }),
  onTimeRate: decimal('on_time_rate', { precision: 5, scale: 2 }),
  
  // Financial
  totalEarnings: decimal('total_earnings', { precision: 12, scale: 2 }).default('0'),
  currentBalance: decimal('current_balance', { precision: 10, scale: 2 }).default('0'),
  
  // Settings
  notificationPreferences: jsonb('notification_preferences'),
  serviceRadius: integer('service_radius').default(25), // miles
  
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
})

// Vendor services
export const vendorServices = pgTable('vendor_services', {
  id: uuid('id').defaultRandom().primaryKey(),
  vendorId: uuid('vendor_id').references(() => vendors.id),
  serviceId: uuid('service_id'), // References services table
  
  // Service-specific details
  subServices: jsonb('sub_services').$type<string[]>(),
  experienceYears: integer('experience_years'),
  
  // Licensing
  requiresLicense: boolean('requires_license').default(false),
  licenseNumber: text('license_number'),
  licenseExpiry: timestamp('license_expiry'),
  licenseVerified: boolean('license_verified').default(false),
  
  // Pricing (optional overrides)
  customPricing: jsonb('custom_pricing'),
  
  active: boolean('active').default(true),
  createdAt: timestamp('created_at').defaultNow()
})

// Service areas
export const vendorServiceAreas = pgTable('vendor_service_areas', {
  id: uuid('id').defaultRandom().primaryKey(),
  vendorId: uuid('vendor_id').references(() => vendors.id),
  
  // Coverage type
  coverageType: text('coverage_type').notNull(), // 'metro', 'county', 'city', 'zip'
  
  // Area details
  metroAreas: jsonb('metro_areas').$type<string[]>(),
  counties: jsonb('counties').$type<string[]>(),
  cities: jsonb('cities').$type<string[]>(),
  zipCodes: jsonb('zip_codes').$type<string[]>(),
  
  maxDistance: integer('max_distance'), // miles from base
  
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
})

// Vendor documents
export const vendorDocuments = pgTable('vendor_documents', {
  id: uuid('id').defaultRandom().primaryKey(),
  vendorId: uuid('vendor_id').references(() => vendors.id),
  
  documentType: documentTypeEnum('document_type').notNull(),
  documentName: text('document_name').notNull(),
  fileUrl: text('file_url').notNull(), // R2 URL
  fileSize: integer('file_size'),
  
  // Document details
  issueDate: timestamp('issue_date'),
  expiryDate: timestamp('expiry_date'),
  documentNumber: text('document_number'),
  
  // Verification
  status: documentStatusEnum('status').notNull().default('pending'),
  verifiedBy: text('verified_by'),
  verifiedAt: timestamp('verified_at'),
  rejectionReason: text('rejection_reason'),
  
  // Reminders
  reminderSent30Days: boolean('reminder_sent_30_days').default(false),
  reminderSent60Days: boolean('reminder_sent_60_days').default(false),
  
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
})

// Team members
export const vendorTeamMembers = pgTable('vendor_team_members', {
  id: uuid('id').defaultRandom().primaryKey(),
  vendorId: uuid('vendor_id').references(() => vendors.id),
  
  name: text('name').notNull(),
  email: text('email').notNull(),
  phone: text('phone'),
  role: text('role').notNull(), // 'owner', 'manager', 'crew_lead', 'worker'
  
  // Permissions
  permissions: jsonb('permissions').$type<string[]>(),
  canAcceptJobs: boolean('can_accept_jobs').default(false),
  canUpdateProgress: boolean('can_update_progress').default(true),
  canViewFinancials: boolean('can_view_financials').default(false),
  
  // Auth
  inviteToken: text('invite_token'),
  invitedAt: timestamp('invited_at'),
  acceptedAt: timestamp('accepted_at'),
  userId: text('user_id'), // If they create an account
  
  active: boolean('active').default(true),
  createdAt: timestamp('created_at').defaultNow()
})

// Job responses
export const vendorJobResponses = pgTable('vendor_job_responses', {
  id: uuid('id').defaultRandom().primaryKey(),
  vendorId: uuid('vendor_id').references(() => vendors.id),
  projectId: uuid('project_id'), // References projects table
  
  status: jobResponseEnum('status').notNull().default('pending'),
  
  // Availability
  availableSlots: jsonb('available_slots').$type<{
    start: string,
    end: string
  }[]>(),
  proposedStartDate: timestamp('proposed_start_date'),
  estimatedDuration: integer('estimated_duration'), // hours
  crewSize: integer('crew_size'),
  
  // Response details
  message: text('message'),
  respondedAt: timestamp('responded_at'),
  respondedBy: uuid('responded_by'), // team member id
  
  // Selection
  selectedAt: timestamp('selected_at'),
  rejectedAt: timestamp('rejected_at'),
  
  createdAt: timestamp('created_at').defaultNow()
})

// Vendor availability
export const vendorAvailability = pgTable('vendor_availability', {
  id: uuid('id').defaultRandom().primaryKey(),
  vendorId: uuid('vendor_id').references(() => vendors.id),
  
  // Recurring availability
  dayOfWeek: integer('day_of_week'), // 0-6
  startTime: text('start_time'), // HH:MM
  endTime: text('end_time'), // HH:MM
  
  // Specific dates
  specificDate: timestamp('specific_date'),
  isAvailable: boolean('is_available').default(true),
  
  // Blocked dates
  blockedFrom: timestamp('blocked_from'),
  blockedTo: timestamp('blocked_to'),
  blockReason: text('block_reason'),
  
  createdAt: timestamp('created_at').defaultNow()
})

// Financial records
export const vendorPayouts = pgTable('vendor_payouts', {
  id: uuid('id').defaultRandom().primaryKey(),
  vendorId: uuid('vendor_id').references(() => vendors.id),
  
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  method: text('method').notNull(), // 'ach', 'wire', 'check'
  status: text('status').notNull(), // 'pending', 'processing', 'completed', 'failed'
  
  // Payment details
  transactionId: text('transaction_id'),
  referenceNumber: text('reference_number'),
  
  // Project references
  projectIds: jsonb('project_ids').$type<string[]>(),
  
  // Dates
  initiatedAt: timestamp('initiated_at').defaultNow(),
  processedAt: timestamp('processed_at'),
  completedAt: timestamp('completed_at'),
  
  // Error handling
  failureReason: text('failure_reason'),
  retryCount: integer('retry_count').default(0)
})

// Payment settings
export const vendorPaymentSettings = pgTable('vendor_payment_settings', {
  id: uuid('id').defaultRandom().primaryKey(),
  vendorId: uuid('vendor_id').references(() => vendors.id).unique(),
  
  preferredMethod: text('preferred_method').notNull(), // 'ach', 'wire', 'check'
  
  // ACH Details (encrypted)
  achRoutingNumber: text('ach_routing_number'),
  achAccountNumber: text('ach_account_number'),
  achAccountType: text('ach_account_type'), // 'checking', 'savings'
  
  // Wire Details
  wireBankName: text('wire_bank_name'),
  wireAccountNumber: text('wire_account_number'),
  wireRoutingNumber: text('wire_routing_number'),
  
  // Check Details
  checkMailingAddress: text('check_mailing_address'),
  
  // Tax
  taxIdNumber: text('tax_id_number'), // Encrypted
  w9Uploaded: boolean('w9_uploaded').default(false),
  
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
})
```

## Key Components

### 1. Onboarding Wizard

```tsx
// apps/vendor-dashboard/src/components/onboarding/OnboardingWizard.tsx

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@clear-quote-pro/ui/button'
import { Progress } from '@clear-quote-pro/ui/progress'
import { useVendorStore } from '../../stores/vendor.store'
import { BusinessInfoForm } from './BusinessInfoForm'
import { InsuranceUpload } from './InsuranceUpload'
import { ServiceSelection } from './ServiceSelection'
import { ServiceAreaConfig } from './ServiceAreaConfig'
import { FinancialSetup } from './FinancialSetup'

const steps = [
  'Business Information',
  'Insurance & Licensing',
  'Services Offered',
  'Service Areas',
  'Payment Setup'
]

export function OnboardingWizard() {
  const [currentStep, setCurrentStep] = useState(0)
  const { updateProfile, saveOnboarding } = useVendorStore()
  
  const handleStepComplete = (data: any) => {
    updateProfile(data)
    
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      handleComplete()
    }
  }
  
  const handleComplete = async () => {
    try {
      await saveOnboarding()
      window.location.href = '/dashboard'
    } catch (error) {
      console.error('Onboarding failed:', error)
    }
  }
  
  return (
    <div className="max-w-3xl mx-auto p-6">
      {/* Progress */}
      <div className="mb-8">
        <div className="flex justify-between text-sm mb-4">
          {steps.map((step, index) => (
            <span
              key={step}
              className={`
                ${index <= currentStep 
                  ? 'text-blue-600 font-medium' 
                  : 'text-gray-400'}
              `}
            >
              {index + 1}. {step}
            </span>
          ))}
        </div>
        <Progress value={(currentStep + 1) / steps.length * 100} />
      </div>
      
      {/* Step Content */}
      <div className="bg-white rounded-lg shadow-sm p-8">
        {currentStep === 0 && (
          <BusinessInfoForm onComplete={handleStepComplete} />
        )}
        {currentStep === 1 && (
          <InsuranceUpload onComplete={handleStepComplete} />
        )}
        {currentStep === 2 && (
          <ServiceSelection onComplete={handleStepComplete} />
        )}
        {currentStep === 3 && (
          <ServiceAreaConfig onComplete={handleStepComplete} />
        )}
        {currentStep === 4 && (
          <FinancialSetup onComplete={handleStepComplete} />
        )}
      </div>
      
      {/* Navigation */}
      <div className="flex justify-between mt-6">
        <Button
          variant="outline"
          onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
          disabled={currentStep === 0}
        >
          Previous
        </Button>
        
        <Button
          onClick={() => {
            // Trigger form submission in current step
            const event = new CustomEvent('submitStep')
            window.dispatchEvent(event)
          }}
        >
          {currentStep === steps.length - 1 ? 'Complete Setup' : 'Continue'}
        </Button>
      </div>
    </div>
  )
}
```

### 2. Job Matching & Notifications

```typescript
// apps/vendor-dashboard/src/services/job-matching.ts

import { db } from '../lib/db'
import { vendors, vendorServices, vendorServiceAreas } from '@clear-quote-pro/database/schema'
import { and, eq, inArray } from 'drizzle-orm'

export class JobMatchingService {
  async findMatchingVendors(job: {
    serviceId: string
    propertyZip: string
    propertyCity: string
    requiresLicense: boolean
  }) {
    // Get vendors offering this service
    const vendorsWithService = await db
      .select()
      .from(vendorServices)
      .where(
        and(
          eq(vendorServices.serviceId, job.serviceId),
          eq(vendorServices.active, true)
        )
      )
    
    // Filter by service area
    const vendorsInArea = await this.filterByServiceArea(
      vendorsWithService.map(v => v.vendorId),
      job.propertyZip,
      job.propertyCity
    )
    
    // Check license requirements
    if (job.requiresLicense) {
      const licensedVendors = vendorsWithService.filter(v => 
        v.licenseVerified && 
        v.licenseExpiry && 
        new Date(v.licenseExpiry) > new Date()
      )
      
      return licensedVendors.filter(v => 
        vendorsInArea.includes(v.vendorId)
      )
    }
    
    return vendorsInArea
  }
  
  async filterByServiceArea(
    vendorIds: string[],
    zip: string,
    city: string
  ) {
    const serviceAreas = await db
      .select()
      .from(vendorServiceAreas)
      .where(inArray(vendorServiceAreas.vendorId, vendorIds))
    
    return serviceAreas.filter(area => {
      // Check if vendor covers this area
      if (area.coverageType === 'metro') {
        return true // Simplified - would check metro area
      }
      
      if (area.zipCodes?.includes(zip)) {
        return true
      }
      
      if (area.cities?.includes(city)) {
        return true
      }
      
      return false
    }).map(a => a.vendorId)
  }
  
  async notifyVendors(vendorIds: string[], job: any) {
    for (const vendorId of vendorIds) {
      // Send push notification
      await this.sendPushNotification(vendorId, {
        title: 'New Job Opportunity',
        body: `${job.service} in ${job.city} - $${job.budget}`,
        data: { jobId: job.id }
      })
      
      // Send SMS
      await this.sendSMS(vendorId, 
        `New job: ${job.service} for $${job.budget}. Open app to respond.`
      )
      
      // Send email
      await this.sendEmail(vendorId, {
        subject: 'New Job Match on Clear Quote Pro',
        template: 'new-job',
        data: job
      })
    }
  }
  
  private async sendPushNotification(vendorId: string, notification: any) {
    // Implementation for push notifications
  }
  
  private async sendSMS(vendorId: string, message: string) {
    // Mock SMS implementation
    console.log(`SMS to vendor ${vendorId}: ${message}`)
  }
  
  private async sendEmail(vendorId: string, email: any) {
    // Email implementation with Resend
  }
}
```

### 3. Real-time Updates with SSE

```typescript
// apps/vendor-dashboard/src/lib/sse.ts

export class SSEConnection {
  private eventSource: EventSource | null = null
  private reconnectTimeout: NodeJS.Timeout | null = null
  private reconnectAttempts = 0
  
  constructor(private vendorId: string) {}
  
  connect() {
    const url = `/api/notifications/subscribe?vendorId=${this.vendorId}`
    
    this.eventSource = new EventSource(url)
    
    this.eventSource.onopen = () => {
      console.log('SSE connected')
      this.reconnectAttempts = 0
    }
    
    this.eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleMessage(data)
    }
    
    this.eventSource.onerror = () => {
      this.reconnect()
    }
    
    // Custom event types
    this.eventSource.addEventListener('new-job', (event) => {
      const job = JSON.parse(event.data)
      this.handleNewJob(job)
    })
    
    this.eventSource.addEventListener('job-selected', (event) => {
      const selection = JSON.parse(event.data)
      this.handleJobSelection(selection)
    })
  }
  
  private reconnect() {
    if (this.reconnectAttempts < 5) {
      this.reconnectTimeout = setTimeout(() => {
        this.reconnectAttempts++
        this.connect()
      }, Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000))
    }
  }
  
  private handleMessage(data: any) {
    // Update notification store
    useNotificationStore.getState().addNotification(data)
  }
  
  private handleNewJob(job: any) {
    // Update jobs store
    useJobsStore.getState().addAvailableJob(job)
    
    // Show toast notification
    toast({
      title: 'New Job Available',
      description: `${job.service} - $${job.budget}`,
      action: {
        label: 'View',
        onClick: () => window.location.href = `/jobs/${job.id}`
      }
    })
  }
  
  private handleJobSelection(selection: any) {
    if (selection.selected) {
      toast({
        title: 'Congratulations!',
        description: 'You\'ve been selected for a job',
        variant: 'success'
      })
    }
  }
  
  disconnect() {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }
    
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
    }
  }
}

// Hook for SSE connection
export function useRealtime() {
  const { vendor } = useVendorStore()
  const [connection, setConnection] = useState<SSEConnection | null>(null)
  
  useEffect(() => {
    if (vendor?.id) {
      const sse = new SSEConnection(vendor.id)
      sse.connect()
      setConnection(sse)
      
      return () => {
        sse.disconnect()
      }
    }
  }, [vendor?.id])
  
  return connection
}
```

### 4. Document Verification Service

```typescript
// apps/vendor-dashboard/src/services/document-verification.ts

import { db } from '../lib/db'
import { vendorDocuments } from '@clear-quote-pro/database/schema'
import { eq, and, lt } from 'drizzle-orm'

export class DocumentVerificationService {
  async verifyInsurance(documentId: string) {
    // Mock verification - in production would call insurance verification API
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const isValid = Math.random() > 0.1 // 90% success rate for testing
    
    if (isValid) {
      await db
        .update(vendorDocuments)
        .set({
          status: 'verified',
          verifiedAt: new Date(),
          verifiedBy: 'system'
        })
        .where(eq(vendorDocuments.id, documentId))
      
      return { success: true }
    } else {
      await db
        .update(vendorDocuments)
        .set({
          status: 'rejected',
          rejectionReason: 'Unable to verify insurance coverage'
        })
        .where(eq(vendorDocuments.id, documentId))
      
      return { 
        success: false, 
        reason: 'Unable to verify insurance coverage' 
      }
    }
  }
  
  async checkExpiringDocuments() {
    const sixtyDaysFromNow = new Date()
    sixtyDaysFromNow.setDate(sixtyDaysFromNow.getDate() + 60)
    
    const thirtyDaysFromNow = new Date()
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)
    
    // Find documents expiring in 60 days
    const expiring60 = await db
      .select()
      .from(vendorDocuments)
      .where(
        and(
          eq(vendorDocuments.status, 'verified'),
          lt(vendorDocuments.expiryDate, sixtyDaysFromNow),
          eq(vendorDocuments.reminderSent60Days, false)
        )
      )
    
    // Send 60-day reminders
    for (const doc of expiring60) {
      await this.sendExpirationReminder(doc, 60)
      
      await db
        .update(vendorDocuments)
        .set({ reminderSent60Days: true })
        .where(eq(vendorDocuments.id, doc.id))
    }
    
    // Find documents expiring in 30 days
    const expiring30 = await db
      .select()
      .from(vendorDocuments)
      .where(
        and(
          eq(vendorDocuments.status, 'verified'),
          lt(vendorDocuments.expiryDate, thirtyDaysFromNow),
          eq(vendorDocuments.reminderSent30Days, false)
        )
      )
    
    // Send 30-day reminders
    for (const doc of expiring30) {
      await this.sendExpirationReminder(doc, 30)
      
      await db
        .update(vendorDocuments)
        .set({ reminderSent30Days: true })
        .where(eq(vendorDocuments.id, doc.id))
    }
  }
  
  private async sendExpirationReminder(document: any, daysUntilExpiry: number) {
    // Send email/SMS notification
    console.log(`Document ${document.documentType} expires in ${daysUntilExpiry} days`)
  }
}
```

### 5. Job Progress Tracking

```tsx
// apps/vendor-dashboard/src/components/jobs/JobProgress.tsx

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Button } from '@clear-quote-pro/ui/button'
import { Textarea } from '@clear-quote-pro/ui/textarea'
import { Checkbox } from '@clear-quote-pro/ui/checkbox'
import { PhotoUpload } from '../shared/PhotoUpload'
import { useJobsStore } from '../../stores/jobs.store'

export function JobProgress({ jobId }: { jobId: string }) {
  const { updateProgress, jobs } = useJobsStore()
  const job = jobs.find(j => j.id === jobId)
  const [photos, setPhotos] = useState<string[]>([])
  
  const { register, handleSubmit } = useForm()
  
  const onSubmit = async (data: any) => {
    await updateProgress(jobId, {
      ...data,
      photos,
      timestamp: new Date()
    })
    
    // Show success message
    toast({
      title: 'Progress Updated',
      description: 'The customer has been notified'
    })
  }
  
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">Today's Progress</h3>
        
        {/* Task Checklist */}
        <div className="space-y-3">
          {job?.tasks?.map((task: any) => (
            <div key={task.id} className="flex items-center">
              <Checkbox
                id={task.id}
                {...register(`tasks.${task.id}`)}
              />
              <label htmlFor={task.id} className="ml-2">
                {task.name}
              </label>
            </div>
          ))}
        </div>
      </div>
      
      {/* Photo Upload */}
      <div>
        <h4 className="font-medium mb-2">Progress Photos</h4>
        <PhotoUpload
          onUpload={(urls) => setPhotos([...photos, ...urls])}
          maxFiles={5}
        />
        
        {/* Photo Preview */}
        <div className="grid grid-cols-3 gap-2 mt-4">
          {photos.map((photo, index) => (
            <div key={index} className="relative">
              <img
                src={photo}
                alt={`Progress ${index + 1}`}
                className="w-full h-24 object-cover rounded"
              />
              <button
                type="button"
                onClick={() => setPhotos(photos.filter((_, i) => i !== index))}
                className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      </div>
      
      {/* Notes */}
      <div>
        <label className="block font-medium mb-2">
          Notes for Customer
        </label>
        <Textarea
          {...register('notes')}
          placeholder="Describe today's progress..."
          rows={4}
        />
      </div>
      
      {/* Completion Status */}
      <div>
        <label className="block font-medium mb-2">
          Project Status
        </label>
        <select {...register('status')} className="w-full border rounded p-2">
          <option value="on_track">On Track</option>
          <option value="ahead">Ahead of Schedule</option>
          <option value="delayed">Delayed</option>
        </select>
      </div>
      
      <div className="flex gap-4">
        <Button type="button" variant="outline">
          Save Draft
        </Button>
        <Button type="submit">
          Send Update
        </Button>
      </div>
    </form>
  )
}
```

## API Routes

### Job Management APIs

```typescript
// apps/vendor-dashboard/src/pages/api/jobs/accept.ts

import type { APIRoute } from 'astro'
import { db } from '../../../lib/db'
import { vendorJobResponses } from '@clear-quote-pro/database/schema'
import { z } from 'zod'

const acceptJobSchema = z.object({
  projectId: z.string().uuid(),
  availableSlots: z.array(z.object({
    start: z.string(),
    end: z.string()
  })),
  proposedStartDate: z.string(),
  estimatedDuration: z.number(),
  crewSize: z.number(),
  message: z.string().optional()
})

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const session = locals.session
    if (!session) {
      return new Response('Unauthorized', { status: 401 })
    }
    
    const vendor = await getVendorByUserId(session.user.id)
    if (!vendor) {
      return new Response('Vendor not found', { status: 404 })
    }
    
    const body = await request.json()
    const data = acceptJobSchema.parse(body)
    
    // Check if already responded
    const existing = await db.query.vendorJobResponses.findFirst({
      where: and(
        eq(vendorJobResponses.vendorId, vendor.id),
        eq(vendorJobResponses.projectId, data.projectId)
      )
    })
    
    if (existing) {
      return new Response('Already responded to this job', { status: 400 })
    }
    
    // Create response
    const [response] = await db.insert(vendorJobResponses).values({
      vendorId: vendor.id,
      projectId: data.projectId,
      status: 'accepted',
      availableSlots: data.availableSlots,
      proposedStartDate: new Date(data.proposedStartDate),
      estimatedDuration: data.estimatedDuration,
      crewSize: data.crewSize,
      message: data.message,
      respondedAt: new Date()
    }).returning()
    
    // Notify homeowner
    await notifyHomeowner(data.projectId, vendor)
    
    return new Response(JSON.stringify({ response }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('Job acceptance failed:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
}
```

## Stores

```typescript
// apps/vendor-dashboard/src/stores/vendor.store.ts

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface VendorState {
  vendor: any
  profile: any
  documents: any[]
  compliance: {
    status: string
    issues: string[]
  }
  
  // Actions
  setVendor: (vendor: any) => void
  updateProfile: (updates: any) => void
  saveOnboarding: () => Promise<void>
  fetchCompliance: () => Promise<void>
  uploadDocument: (doc: any) => Promise<void>
}

export const useVendorStore = create<VendorState>()(
  persist(
    (set, get) => ({
      vendor: null,
      profile: {},
      documents: [],
      compliance: {
        status: 'pending',
        issues: []
      },
      
      setVendor: (vendor) => set({ vendor }),
      
      updateProfile: (updates) => set((state) => ({
        profile: { ...state.profile, ...updates }
      })),
      
      saveOnboarding: async () => {
        const { profile } = get()
        
        const response = await fetch('/api/vendor/onboarding', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(profile)
        })
        
        const vendor = await response.json()
        set({ vendor })
      },
      
      fetchCompliance: async () => {
        const response = await fetch('/api/compliance/status')
        const compliance = await response.json()
        set({ compliance })
      },
      
      uploadDocument: async (doc) => {
        const response = await fetch('/api/compliance/documents', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(doc)
        })
        
        const document = await response.json()
        set((state) => ({
          documents: [...state.documents, document]
        }))
      }
    }),
    {
      name: 'vendor-store'
    }
  )
)
```

## Environment Variables

```bash
# .env.local

# Database
DATABASE_URL="postgresql://user:password@localhost:5432/clearquotepro"
DATABASE_URL_PROD="postgresql://user:<EMAIL>/clearquotepro"

# Auth
AUTH_SECRET="your-auth-secret"

# Cloudflare R2
R2_ACCOUNT_ID="your-account-id"
R2_ACCESS_KEY_ID="your-access-key"
R2_SECRET_ACCESS_KEY="your-secret-key"
R2_BUCKET_NAME="clearquotepro-vendor-docs"

# Email
RESEND_API_KEY="your-resend-key"

# SMS (Mocked initially)
TWILIO_ACCOUNT_SID="mock-sid"
TWILIO_AUTH_TOKEN="mock-token"
TWILIO_PHONE_NUMBER="+**********"

# Insurance Verification API (Future)
INSURANCE_API_KEY="your-insurance-api-key"

# Background Check API (Future)
BACKGROUND_CHECK_API_KEY="your-background-check-key"

# Analytics
PUBLIC_SIMPLE_ANALYTICS_ID="your-analytics-id"

# Public URLs
PUBLIC_APP_URL="http://localhost:4323"
```

## Mobile Considerations

### Progressive Web App
```json
// public/manifest.json
{
  "name": "Clear Quote Pro Vendor",
  "short_name": "CQP Vendor",
  "theme_color": "#1e40af",
  "background_color": "#ffffff",
  "display": "standalone",
  "scope": "/",
  "start_url": "/dashboard",
  "icons": [
    {
      "src": "/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icon-512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

### Mobile-Specific Features
- Touch-optimized job cards
- Swipe to accept/decline jobs
- Camera integration for progress photos
- GPS check-in at job sites
- Offline job viewing
- Push notifications

## Testing Strategy

### Unit Tests
```typescript
// tests/unit/job-matching.test.ts
describe('JobMatchingService', () => {
  it('should match vendors by service and area', async () => {
    const service = new JobMatchingService()
    const vendors = await service.findMatchingVendors({
      serviceId: 'painting',
      propertyZip: '98101',
      propertyCity: 'Seattle',
      requiresLicense: false
    })
    
    expect(vendors.length).toBeGreaterThan(0)
  })
})
```

### E2E Tests
```typescript
// tests/e2e/job-acceptance.spec.ts
test('vendor can accept job', async ({ page }) => {
  await page.goto('/jobs/available')
  await page.click('text=View Details')
  await page.click('text=Accept Job')
  await page.selectOption('[name="availability"]', 'nov-18-19')
  await page.click('text=Submit Acceptance')
  
  await expect(page).toHaveURL('/jobs/active')
})
```

## Deployment Checklist

- [ ] Environment variables configured
- [ ] Database migrations run
- [ ] R2 bucket for documents created
- [ ] Email templates configured
- [ ] SSL certificates active
- [ ] Monitoring configured
- [ ] Error tracking enabled
- [ ] Analytics installed
- [ ] Mobile PWA tested
- [ ] Push notifications configured
- [ ] Background jobs scheduled
- [ ] Document verification automated
- [ ] Payment processing tested
- [ ] Compliance checks automated
- [ ] Performance optimized