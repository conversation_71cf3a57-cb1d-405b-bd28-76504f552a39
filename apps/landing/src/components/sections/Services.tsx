import { Card } from '@repo/ui/card';
import { Badge } from '@repo/ui/badge';
import { 
  Paintbrush, 
  Wrench, 
  Zap, 
  Droplets, 
  Wind, 
  Home,
  Hammer,
  TreePine,
  Sparkles,
  Thermometer,
  Shield,
  Settings,
  ArrowRight,
  Star
} from 'lucide-react';
import { useEffect, useState } from 'react';

const services = [
  {
    name: "Interior Painting",
    price: "$1.50",
    unit: "per sq ft",
    icon: Paintbrush,
    popular: true,
    gradient: "from-terracotta/20 to-terracotta-light/20",
    iconColor: "text-terracotta"
  },
  {
    name: "Plumbing",
    price: "$125",
    unit: "per hour",
    icon: Droplets,
    popular: false,
    gradient: "from-teal/20 to-teal-light/20",
    iconColor: "text-teal"
  },
  {
    name: "Electrical",
    price: "$150",
    unit: "per hour",
    icon: Zap,
    popular: false,
    gradient: "from-forest/20 to-forest-light/20",
    iconColor: "text-forest"
  },
  {
    name: "HVAC Service",
    price: "$189",
    unit: "full inspection",
    icon: Wind,
    popular: true,
    gradient: "from-teal/20 to-ocean/20",
    iconColor: "text-teal"
  },
  {
    name: "Flooring",
    price: "$4.25",
    unit: "per sq ft",
    icon: Home,
    popular: false,
    gradient: "from-sage/20 to-sage/10",
    iconColor: "text-sage"
  },
  {
    name: "Handyman",
    price: "$85",
    unit: "per hour",
    icon: Hammer,
    popular: true,
    gradient: "from-forest/20 to-forest-light/20",
    iconColor: "text-forest"
  },
  {
    name: "Landscaping",
    price: "$75",
    unit: "per visit",
    icon: TreePine,
    popular: false,
    gradient: "from-forest/20 to-sage/20",
    iconColor: "text-forest"
  },
  {
    name: "Deep Cleaning",
    price: "$0.35",
    unit: "per sq ft",
    icon: Sparkles,
    popular: false,
    gradient: "from-teal/20 to-teal-light/20",
    iconColor: "text-teal"
  },
  {
    name: "Appliance Install",
    price: "$149",
    unit: "standard install",
    icon: Settings,
    popular: false,
    gradient: "from-forest/20 to-forest-light/20",
    iconColor: "text-forest"
  },
  {
    name: "Gutter Cleaning",
    price: "$189",
    unit: "single story",
    icon: Shield,
    popular: false,
    gradient: "from-terracotta/20 to-terracotta-light/20",
    iconColor: "text-terracotta"
  },
  {
    name: "HVAC Filter",
    price: "$49",
    unit: "replacement",
    icon: Thermometer,
    popular: false,
    gradient: "from-teal/20 to-teal-light/20",
    iconColor: "text-teal"
  },
  {
    name: "General Repairs",
    price: "$95",
    unit: "per hour",
    icon: Wrench,
    popular: false,
    gradient: "from-forest/20 to-forest-light/20",
    iconColor: "text-forest"
  }
];

export default function Services() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background design */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-cream/30 to-background" />
        <div className="absolute inset-0 dot-pattern opacity-[0.015]" />
      </div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Section header with animation */}
          <div className={`text-center mb-16 transition-all duration-1000 ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4'}`}>
            <Badge variant="secondary" className="mb-4 bg-forest/10 text-forest border-forest/20">
              <Star className="w-3 h-3 mr-1 fill-current" />
              20+ Services Available
            </Badge>
            <h2 className="mb-4">
              <span className="text-foreground">Every Service.</span>
              <span className="gradient-text ml-3">One Platform.</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Transparent pricing for all your home needs. No quotes, no surprises—just honest rates from verified professionals.
            </p>
          </div>
          
          {/* Services grid with staggered animation */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
            {services.map((service, index) => {
              const Icon = service.icon;
              const isHovered = hoveredIndex === index;
              
              return (
                <div
                  key={service.name}
                  className={`transition-all duration-700 ${
                    mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
                  }`}
                  style={{ transitionDelay: `${index * 50}ms` }}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                >
                  <Card 
                    className={`
                      relative p-6 h-full cursor-pointer 
                      border transition-all duration-300 group
                      ${service.popular 
                        ? 'border-forest/30 shadow-forest/10 shadow-lg' 
                        : 'border-border hover:border-forest/20'
                      }
                      hover:shadow-xl hover:-translate-y-1
                      ${isHovered ? 'bg-gradient-to-br ' + service.gradient : 'bg-card'}
                    `}
                  >
                    {/* Popular badge */}
                    {service.popular && (
                      <Badge 
                        className="absolute -top-2 -right-2 bg-gradient-warm text-white border-0 text-xs px-2 py-1"
                      >
                        Popular
                      </Badge>
                    )}
                    
                    {/* Icon with animated background */}
                    <div className="mb-4">
                      <div className={`
                        inline-flex p-3 rounded-lg transition-all duration-300
                        ${isHovered ? 'bg-white/80 shadow-lg scale-110' : 'bg-background/50'}
                      `}>
                        <Icon className={`w-6 h-6 ${service.iconColor} transition-transform duration-300 ${
                          isHovered ? 'rotate-12' : ''
                        }`} />
                      </div>
                    </div>
                    
                    {/* Service name */}
                    <h3 className="font-semibold text-foreground mb-3 group-hover:text-forest transition-colors">
                      {service.name}
                    </h3>
                    
                    {/* Pricing */}
                    <div className="space-y-1">
                      <p className={`text-2xl font-bold transition-colors ${
                        isHovered ? 'text-forest' : 'text-foreground'
                      }`}>
                        {service.price}
                      </p>
                      <p className="text-sm text-muted-foreground">{service.unit}</p>
                    </div>
                    
                    {/* Hover action */}
                    <div className={`
                      absolute bottom-6 right-6 
                      transition-all duration-300
                      ${isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-2'}
                    `}>
                      <ArrowRight className="w-4 h-4 text-forest" />
                    </div>
                  </Card>
                </div>
              );
            })}
          </div>
          
          {/* Call to action */}
          <div className={`
            mt-16 text-center glass rounded-2xl p-8 
            transition-all duration-1000 delay-500
            ${mounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}
          `}>
            <h3 className="text-2xl font-semibold mb-3">
              Can't find what you need?
            </h3>
            <p className="text-muted-foreground mb-6">
              We're constantly adding new services. Tell us what you're looking for.
            </p>
            <button className="px-6 py-3 bg-forest text-white rounded-lg hover:bg-forest-light transition-colors inline-flex items-center gap-2 hover-lift">
              Request a Service
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
          
          {/* Footer note with better styling */}
          <div className={`
            mt-12 text-center transition-all duration-1000 delay-700
            ${mounted ? 'opacity-100' : 'opacity-0'}
          `}>
            <p className="text-sm text-muted-foreground flex items-center justify-center gap-2">
              <Shield className="w-3.5 h-3.5" />
              All contractors are licensed, insured, and background-checked
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}