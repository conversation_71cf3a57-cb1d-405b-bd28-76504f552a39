import { Shield, Award, Users, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Star } from 'lucide-react';

const trustPoints = [
  {
    icon: Shield,
    title: "Licensed & Insured",
    description: "Every contractor carries proper licensing and $1M+ insurance coverage."
  },
  {
    icon: FileCheck,
    title: "Background Checked",
    description: "Comprehensive criminal and reference checks for every professional."
  },
  {
    icon: Award,
    title: "Quality Guaranteed",
    description: "60-day warranty on all work. We'll make it right or refund you."
  },
  {
    icon: Users,
    title: "10,000+ Jobs Done",
    description: "Trusted by thousands of homeowners and property managers."
  },
  {
    icon: Clock,
    title: "24hr Response",
    description: "Get matched with available pros within one business day."
  },
  {
    icon: Star,
    title: "4.8 Average Rating",
    description: "Consistently high ratings from verified customers."
  }
];

export default function Trust() {
  return (
    <section className="py-24 bg-gray-50/50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Section header */}
          <div className="text-center mb-16">
            <h2 className="text-gray-900 mb-4">
              Why Homeowners Trust Clear Quote
            </h2>
            <p className="text-xl text-gray-600">
              We've eliminated everything that makes home repairs stressful.
            </p>
          </div>
          
          {/* Trust grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {trustPoints.map((point, index) => {
              const Icon = point.icon;
              return (
                <div key={index} className="group">
                  <div className="flex gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 rounded-xl bg-white shadow-sm flex items-center justify-center group-hover:shadow-md transition-shadow">
                        <Icon className="w-6 h-6 text-gray-700" />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {point.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {point.description}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
          
          {/* Bottom stats */}
          <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 py-8 border-t border-gray-200">
            <Stat value="2,500+" label="Properties Serviced" />
            <Stat value="$2M+" label="Work Completed" />
            <Stat value="100%" label="Insured Vendors" />
            <Stat value="60 Day" label="Quality Guarantee" />
          </div>
        </div>
      </div>
    </section>
  );
}

function Stat({ value, label }: { value: string; label: string }) {
  return (
    <div className="text-center">
      <p className="text-3xl font-bold text-gray-900 mb-1">{value}</p>
      <p className="text-sm text-gray-600">{label}</p>
    </div>
  );
}