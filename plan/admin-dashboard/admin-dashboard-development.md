# Admin Dashboard Development - Clear Quote Pro

## Tech Stack

### Core Technologies
- **Framework**: Astro 5.x (Hybrid rendering)
- **UI Framework**: React 19.x (Islands architecture)
- **Styling**: Tailwind CSS 4.x
- **Component Library**: shadcn/ui (shared package)
- **State Management**: Zustand
- **Forms**: React Hook Form + Zod
- **Tables**: TanStack Table
- **Charts**: Recharts
- **Animations**: anime.js 4.x
- **Database ORM**: Drizzle
- **Authentication**: Better Auth with Admin roles
- **File Storage**: Cloudflare R2
- **Real-time**: Server-Sent Events (SSE)
- **Email**: Resend
- **Analytics**: Internal + Simple Analytics
- **Monitoring**: Sentry
- **Deployment**: Vercel

## Project Structure

```
apps/admin-dashboard/
├── src/
│   ├── components/
│   │   ├── dashboard/
│   │   │   ├── DashboardLayout.tsx
│   │   │   ├── StatsGrid.tsx
│   │   │   ├── PendingActions.tsx
│   │   │   ├── RecentActivity.tsx
│   │   │   ├── SystemStatus.tsx
│   │   │   └── QuickActions.tsx
│   │   ├── vendors/
│   │   │   ├── VendorQueue.tsx
│   │   │   ├── VendorDetail.tsx
│   │   │   ├── DocumentReview.tsx
│   │   │   ├── VendorApproval.tsx
│   │   │   ├── VendorSearch.tsx
│   │   │   └── VendorMetrics.tsx
│   │   ├── users/
│   │   │   ├── UserTable.tsx
│   │   │   ├── UserDetail.tsx
│   │   │   ├── UserSearch.tsx
│   │   │   ├── UserActions.tsx
│   │   │   └── UserActivity.tsx
│   │   ├── documents/
│   │   │   ├── DocumentQueue.tsx
│   │   │   ├── DocumentViewer.tsx
│   │   │   ├── VerificationForm.tsx
│   │   │   ├── BulkActions.tsx
│   │   │   └── ExpirationTracker.tsx
│   │   ├── disputes/
│   │   │   ├── DisputeList.tsx
│   │   │   ├── DisputeDetail.tsx
│   │   │   ├── ResolutionForm.tsx
│   │   │   ├── Evidence.tsx
│   │   │   └── DisputeTimeline.tsx
│   │   ├── financial/
│   │   │   ├── RevenueChart.tsx
│   │   │   ├── PayoutManager.tsx
│   │   │   ├── TransactionTable.tsx
│   │   │   ├── FeeCalculator.tsx
│   │   │   └── FinancialReports.tsx
│   │   ├── services/
│   │   │   ├── ServiceList.tsx
│   │   │   ├── ServiceEditor.tsx
│   │   │   ├── PricingRules.tsx
│   │   │   ├── RegionalSettings.tsx
│   │   │   └── ServiceMetrics.tsx
│   │   ├── communications/
│   │   │   ├── BroadcastComposer.tsx
│   │   │   ├── MessageTemplates.tsx
│   │   │   ├── NotificationManager.tsx
│   │   │   └── CommunicationLog.tsx
│   │   ├── analytics/
│   │   │   ├── AnalyticsDashboard.tsx
│   │   │   ├── MetricsGrid.tsx
│   │   │   ├── ReportBuilder.tsx
│   │   │   ├── ExportManager.tsx
│   │   │   └── KPITracker.tsx
│   │   ├── support/
│   │   │   ├── TicketList.tsx
│   │   │   ├── TicketDetail.tsx
│   │   │   ├── TicketAssignment.tsx
│   │   │   └── SupportMetrics.tsx
│   │   ├── settings/
│   │   │   ├── PlatformSettings.tsx
│   │   │   ├── BusinessRules.tsx
│   │   │   ├── FeatureToggles.tsx
│   │   │   ├── IntegrationConfig.tsx
│   │   │   └── AdminUsers.tsx
│   │   ├── audit/
│   │   │   ├── AuditLog.tsx
│   │   │   ├── ActivityMonitor.tsx
│   │   │   ├── ComplianceReport.tsx
│   │   │   └── SecurityAlerts.tsx
│   │   └── shared/
│   │       ├── AdminNav.tsx
│   │       ├── SearchBar.tsx
│   │       ├── DataTable.tsx
│   │       ├── FilterPanel.tsx
│   │       ├── ActionMenu.tsx
│   │       ├── StatusBadge.tsx
│   │       ├── ConfirmDialog.tsx
│   │       └── NotificationBell.tsx
│   ├── layouts/
│   │   ├── BaseLayout.astro
│   │   ├── AdminLayout.astro
│   │   └── ReportLayout.astro
│   ├── pages/
│   │   ├── index.astro                      # Dashboard
│   │   ├── auth/
│   │   │   ├── login.astro
│   │   │   └── two-factor.astro
│   │   ├── dashboard.astro
│   │   ├── vendors/
│   │   │   ├── index.astro                  # Vendor list
│   │   │   ├── pending.astro                # Verification queue
│   │   │   ├── [id]/
│   │   │   │   ├── index.astro
│   │   │   │   ├── documents.astro
│   │   │   │   ├── history.astro
│   │   │   │   └── approve.astro
│   │   │   └── reports.astro
│   │   ├── users/
│   │   │   ├── index.astro
│   │   │   ├── [id]/
│   │   │   │   ├── index.astro
│   │   │   │   ├── activity.astro
│   │   │   │   └── edit.astro
│   │   │   └── export.astro
│   │   ├── documents/
│   │   │   ├── index.astro
│   │   │   ├── pending.astro
│   │   │   └── expired.astro
│   │   ├── disputes/
│   │   │   ├── index.astro
│   │   │   ├── [id]/
│   │   │   │   ├── index.astro
│   │   │   │   └── resolve.astro
│   │   │   └── reports.astro
│   │   ├── financial/
│   │   │   ├── index.astro
│   │   │   ├── payouts.astro
│   │   │   ├── transactions.astro
│   │   │   └── reports.astro
│   │   ├── services/
│   │   │   ├── index.astro
│   │   │   ├── [id]/edit.astro
│   │   │   └── pricing.astro
│   │   ├── communications/
│   │   │   ├── broadcast.astro
│   │   │   ├── templates.astro
│   │   │   └── history.astro
│   │   ├── analytics/
│   │   │   ├── index.astro
│   │   │   ├── reports.astro
│   │   │   └── export.astro
│   │   ├── support/
│   │   │   ├── tickets.astro
│   │   │   └── [id].astro
│   │   ├── settings/
│   │   │   ├── index.astro
│   │   │   ├── platform.astro
│   │   │   ├── integrations.astro
│   │   │   └── admins.astro
│   │   ├── audit/
│   │   │   ├── index.astro
│   │   │   └── export.astro
│   │   └── api/
│   │       ├── auth/
│   │       │   └── [...all].ts
│   │       ├── vendors/
│   │       │   ├── verify.ts
│   │       │   ├── approve.ts
│   │       │   ├── suspend.ts
│   │       │   └── documents.ts
│   │       ├── users/
│   │       │   ├── list.ts
│   │       │   ├── [id].ts
│   │       │   ├── suspend.ts
│   │       │   └── activity.ts
│   │       ├── documents/
│   │       │   ├── review.ts
│   │       │   ├── approve.ts
│   │       │   └── reject.ts
│   │       ├── disputes/
│   │       │   ├── list.ts
│   │       │   ├── [id].ts
│   │       │   └── resolve.ts
│   │       ├── financial/
│   │       │   ├── revenue.ts
│   │       │   ├── payouts.ts
│   │       │   └── process.ts
│   │       ├── services/
│   │       │   ├── list.ts
│   │       │   ├── update.ts
│   │       │   └── pricing.ts
│   │       ├── communications/
│   │       │   ├── broadcast.ts
│   │       │   └── templates.ts
│   │       ├── analytics/
│   │       │   ├── metrics.ts
│   │       │   ├── reports.ts
│   │       │   └── export.ts
│   │       ├── audit/
│   │       │   ├── log.ts
│   │       │   └── activity.ts
│   │       └── system/
│   │           ├── health.ts
│   │           ├── config.ts
│   │           └── notifications.ts
│   ├── lib/
│   │   ├── auth.ts
│   │   ├── db.ts
│   │   ├── permissions.ts
│   │   ├── notifications.ts
│   │   ├── email.ts
│   │   ├── analytics.ts
│   │   ├── export.ts
│   │   ├── constants.ts
│   │   └── utils.ts
│   ├── stores/
│   │   ├── admin.store.ts
│   │   ├── vendors.store.ts
│   │   ├── users.store.ts
│   │   ├── disputes.store.ts
│   │   ├── financial.store.ts
│   │   └── notifications.store.ts
│   ├── hooks/
│   │   ├── useAdmin.ts
│   │   ├── usePermissions.ts
│   │   ├── useRealtime.ts
│   │   ├── useAnalytics.ts
│   │   └── useExport.ts
│   ├── services/
│   │   ├── vendor-verification.ts
│   │   ├── document-processor.ts
│   │   ├── dispute-handler.ts
│   │   ├── payout-processor.ts
│   │   ├── notification-service.ts
│   │   └── analytics-engine.ts
│   ├── middleware/
│   │   └── index.ts
│   └── styles/
│       └── global.css
├── public/
│   ├── images/
│   └── icons/
├── astro.config.mjs
├── tailwind.config.ts
├── tsconfig.json
└── package.json
```

## Database Schema Extensions

```typescript
// packages/database/src/schema/admin.ts

import { 
  pgTable, 
  text, 
  timestamp, 
  uuid, 
  boolean, 
  integer,
  jsonb,
  pgEnum 
} from 'drizzle-orm/pg-core'

// Admin-specific enums
export const adminRoleEnum = pgEnum('admin_role', [
  'super_admin',
  'admin',
  'support',
  'analyst'
])

export const actionTypeEnum = pgEnum('action_type', [
  'vendor_approved',
  'vendor_rejected',
  'vendor_suspended',
  'document_verified',
  'document_rejected',
  'dispute_resolved',
  'payout_processed',
  'user_suspended',
  'settings_changed',
  'broadcast_sent'
])

export const disputeStatusEnum = pgEnum('dispute_status', [
  'open',
  'under_review',
  'pending_vendor',
  'pending_customer',
  'resolved',
  'escalated',
  'closed'
])

// Admin users
export const adminUsers = pgTable('admin_users', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: text('user_id').notNull().unique(), // From Better Auth
  role: adminRoleEnum('role').notNull(),
  
  // Permissions
  permissions: jsonb('permissions').$type<string[]>(),
  
  // Activity
  lastLoginAt: timestamp('last_login_at'),
  lastActivityAt: timestamp('last_activity_at'),
  
  // Settings
  notificationPreferences: jsonb('notification_preferences'),
  dashboardLayout: jsonb('dashboard_layout'),
  
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
})

// Audit log
export const auditLog = pgTable('audit_log', {
  id: uuid('id').defaultRandom().primaryKey(),
  adminId: uuid('admin_id').references(() => adminUsers.id),
  
  actionType: actionTypeEnum('action_type').notNull(),
  entityType: text('entity_type').notNull(), // 'vendor', 'user', 'document', etc.
  entityId: text('entity_id').notNull(),
  
  // Action details
  description: text('description').notNull(),
  previousValue: jsonb('previous_value'),
  newValue: jsonb('new_value'),
  metadata: jsonb('metadata'),
  
  // Request info
  ipAddress: text('ip_address'),
  userAgent: text('user_agent'),
  
  createdAt: timestamp('created_at').defaultNow()
})

// Document verification records
export const documentVerifications = pgTable('document_verifications', {
  id: uuid('id').defaultRandom().primaryKey(),
  documentId: uuid('document_id').notNull(),
  adminId: uuid('admin_id').references(() => adminUsers.id),
  
  action: text('action').notNull(), // 'approved', 'rejected', 'requested_info'
  notes: text('notes'),
  
  // Verification details
  verificationChecks: jsonb('verification_checks').$type<{
    validFormat: boolean,
    validDates: boolean,
    adequateCoverage: boolean,
    legitimateIssuer: boolean
  }>(),
  
  createdAt: timestamp('created_at').defaultNow()
})

// Disputes
export const disputes = pgTable('disputes', {
  id: uuid('id').defaultRandom().primaryKey(),
  projectId: uuid('project_id').notNull(),
  
  // Parties
  customerId: text('customer_id').notNull(),
  vendorId: uuid('vendor_id').notNull(),
  
  // Dispute details
  status: disputeStatusEnum('status').notNull().default('open'),
  priority: text('priority').notNull(), // 'low', 'medium', 'high', 'urgent'
  category: text('category').notNull(), // 'quality', 'payment', 'schedule', 'communication'
  
  // Issue
  customerStatement: text('customer_statement').notNull(),
  vendorResponse: text('vendor_response'),
  
  // Financial
  disputedAmount: decimal('disputed_amount', { precision: 10, scale: 2 }),
  refundAmount: decimal('refund_amount', { precision: 10, scale: 2 }),
  
  // Assignment
  assignedAdminId: uuid('assigned_admin_id').references(() => adminUsers.id),
  assignedAt: timestamp('assigned_at'),
  
  // Resolution
  resolution: text('resolution'),
  resolutionNotes: text('resolution_notes'),
  resolvedBy: uuid('resolved_by').references(() => adminUsers.id),
  resolvedAt: timestamp('resolved_at'),
  
  // Evidence
  evidence: jsonb('evidence').$type<{
    photos: string[],
    documents: string[],
    messages: string[]
  }>(),
  
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
})

// Platform settings
export const platformSettings = pgTable('platform_settings', {
  id: uuid('id').defaultRandom().primaryKey(),
  key: text('key').notNull().unique(),
  value: jsonb('value').notNull(),
  category: text('category').notNull(), // 'general', 'fees', 'requirements', 'features'
  description: text('description'),
  
  updatedBy: uuid('updated_by').references(() => adminUsers.id),
  updatedAt: timestamp('updated_at').defaultNow()
})

// Support tickets
export const supportTickets = pgTable('support_tickets', {
  id: uuid('id').defaultRandom().primaryKey(),
  
  // User info
  userId: text('user_id').notNull(),
  userType: text('user_type').notNull(), // 'customer', 'vendor'
  
  // Ticket details
  subject: text('subject').notNull(),
  description: text('description').notNull(),
  category: text('category').notNull(), // 'technical', 'billing', 'account', 'dispute'
  priority: text('priority').notNull(), // 'low', 'medium', 'high', 'urgent'
  status: text('status').notNull(), // 'open', 'in_progress', 'waiting', 'resolved', 'closed'
  
  // Assignment
  assignedTo: uuid('assigned_to').references(() => adminUsers.id),
  assignedAt: timestamp('assigned_at'),
  
  // Resolution
  resolution: text('resolution'),
  resolvedAt: timestamp('resolved_at'),
  closedAt: timestamp('closed_at'),
  
  // Metrics
  firstResponseAt: timestamp('first_response_at'),
  satisfactionRating: integer('satisfaction_rating'), // 1-5
  
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
})

// Broadcast messages
export const broadcastMessages = pgTable('broadcast_messages', {
  id: uuid('id').defaultRandom().primaryKey(),
  
  // Message details
  subject: text('subject').notNull(),
  content: text('content').notNull(),
  messageType: text('message_type').notNull(), // 'maintenance', 'feature', 'policy', 'promotional'
  
  // Recipients
  recipientType: text('recipient_type').notNull(), // 'all', 'customers', 'vendors', 'region'
  recipientFilter: jsonb('recipient_filter'), // Additional filtering criteria
  
  // Delivery
  channels: jsonb('channels').$type<string[]>(), // ['email', 'in_app', 'sms']
  scheduledFor: timestamp('scheduled_for'),
  sentAt: timestamp('sent_at'),
  
  // Metrics
  recipientCount: integer('recipient_count'),
  deliveredCount: integer('delivered_count'),
  readCount: integer('read_count'),
  
  createdBy: uuid('created_by').references(() => adminUsers.id),
  createdAt: timestamp('created_at').defaultNow()
})

// Analytics snapshots
export const analyticsSnapshots = pgTable('analytics_snapshots', {
  id: uuid('id').defaultRandom().primaryKey(),
  
  date: timestamp('date').notNull(),
  period: text('period').notNull(), // 'daily', 'weekly', 'monthly'
  
  // User metrics
  totalUsers: integer('total_users'),
  newUsers: integer('new_users'),
  activeUsers: integer('active_users'),
  
  // Vendor metrics
  totalVendors: integer('total_vendors'),
  activeVendors: integer('active_vendors'),
  pendingVendors: integer('pending_vendors'),
  
  // Job metrics
  totalJobs: integer('total_jobs'),
  completedJobs: integer('completed_jobs'),
  averageJobValue: decimal('average_job_value', { precision: 10, scale: 2 }),
  
  // Financial metrics
  totalRevenue: decimal('total_revenue', { precision: 12, scale: 2 }),
  platformFees: decimal('platform_fees', { precision: 12, scale: 2 }),
  
  // Quality metrics
  averageRating: decimal('average_rating', { precision: 3, scale: 2 }),
  disputeRate: decimal('dispute_rate', { precision: 5, scale: 2 }),
  completionRate: decimal('completion_rate', { precision: 5, scale: 2 }),
  
  createdAt: timestamp('created_at').defaultNow()
})
```

## Key Components

### 1. Admin Authentication & Permissions

```typescript
// apps/admin-dashboard/src/lib/auth.ts

import { betterAuth } from 'better-auth'
import { drizzleAdapter } from 'better-auth/adapters/drizzle'
import { db } from './db'
import { adminUsers } from '@clear-quote-pro/database/schema'

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: 'pg'
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false
  },
  twoFactor: {
    enabled: true,
    issuer: 'Clear Quote Pro Admin'
  },
  session: {
    expiresIn: 60 * 60 * 8, // 8 hours for admin sessions
    updateAge: 60 * 60 // 1 hour
  },
  callbacks: {
    session: async ({ session, user }) => {
      // Get admin role and permissions
      const admin = await db.query.adminUsers.findFirst({
        where: eq(adminUsers.userId, user.id)
      })
      
      if (!admin) {
        throw new Error('Not authorized as admin')
      }
      
      return {
        ...session,
        user: {
          ...session.user,
          role: admin.role,
          permissions: admin.permissions
        }
      }
    }
  }
})

// Permission checker
export function hasPermission(
  user: any,
  permission: string
): boolean {
  if (user.role === 'super_admin') return true
  
  const rolePermissions: Record<string, string[]> = {
    admin: [
      'vendors.manage',
      'users.manage',
      'disputes.resolve',
      'documents.verify',
      'payouts.process'
    ],
    support: [
      'users.view',
      'disputes.view',
      'disputes.comment',
      'tickets.manage'
    ],
    analyst: [
      'analytics.view',
      'reports.generate',
      'data.export'
    ]
  }
  
  const userPermissions = [
    ...(rolePermissions[user.role] || []),
    ...(user.permissions || [])
  ]
  
  return userPermissions.includes(permission)
}
```

### 2. Vendor Verification Component

```tsx
// apps/admin-dashboard/src/components/vendors/VendorApproval.tsx

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Button } from '@clear-quote-pro/ui/button'
import { Card } from '@clear-quote-pro/ui/card'
import { Badge } from '@clear-quote-pro/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@clear-quote-pro/ui/tabs'
import { DocumentViewer } from '../documents/DocumentViewer'
import { useVendorStore } from '../../stores/vendors.store'

export function VendorApproval({ vendorId }: { vendorId: string }) {
  const { vendor, documents, approveVendor, rejectVendor } = useVendorStore()
  const [verificationNotes, setVerificationNotes] = useState('')
  
  const handleApprove = async () => {
    try {
      await approveVendor(vendorId, {
        notes: verificationNotes,
        verifiedDocuments: documents.map(d => d.id)
      })
      
      // Send notification to vendor
      await fetch('/api/vendors/notify', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          vendorId,
          type: 'approval',
          message: 'Your vendor account has been approved!'
        })
      })
      
      toast({
        title: 'Vendor Approved',
        description: 'Notification sent to vendor',
        variant: 'success'
      })
      
      window.location.href = '/vendors/pending'
    } catch (error) {
      toast({
        title: 'Approval Failed',
        description: error.message,
        variant: 'error'
      })
    }
  }
  
  const handleReject = async (reason: string) => {
    try {
      await rejectVendor(vendorId, {
        reason,
        notes: verificationNotes
      })
      
      // Send notification with reason
      await fetch('/api/vendors/notify', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          vendorId,
          type: 'rejection',
          message: `Application rejected: ${reason}`
        })
      })
      
      toast({
        title: 'Vendor Rejected',
        description: 'Vendor has been notified',
        variant: 'info'
      })
      
      window.location.href = '/vendors/pending'
    } catch (error) {
      toast({
        title: 'Rejection Failed',
        description: error.message,
        variant: 'error'
      })
    }
  }
  
  return (
    <div className="space-y-6">
      {/* Vendor Overview */}
      <Card className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-2xl font-bold">{vendor.businessName}</h2>
            <p className="text-gray-600">{vendor.businessType} • {vendor.yearsInBusiness} years</p>
          </div>
          <Badge variant={vendor.status === 'pending' ? 'warning' : 'success'}>
            {vendor.status}
          </Badge>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500">Owner</p>
            <p className="font-medium">{vendor.ownerName}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Location</p>
            <p className="font-medium">{vendor.city}, {vendor.state}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Services</p>
            <p className="font-medium">{vendor.services.length} services</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Coverage</p>
            <p className="font-medium">{vendor.serviceRadius} miles</p>
          </div>
        </div>
      </Card>
      
      {/* Document Verification */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Document Verification</h3>
        
        <Tabs defaultValue="insurance">
          <TabsList>
            <TabsTrigger value="insurance">Insurance</TabsTrigger>
            <TabsTrigger value="license">Licenses</TabsTrigger>
            <TabsTrigger value="tax">Tax Forms</TabsTrigger>
            <TabsTrigger value="background">Background</TabsTrigger>
          </TabsList>
          
          <TabsContent value="insurance" className="mt-4">
            {documents.filter(d => d.type === 'insurance').map(doc => (
              <DocumentVerificationCard
                key={doc.id}
                document={doc}
                onVerify={() => verifyDocument(doc.id)}
                onReject={() => rejectDocument(doc.id)}
              />
            ))}
          </TabsContent>
          
          <TabsContent value="license" className="mt-4">
            {documents.filter(d => d.type === 'license').map(doc => (
              <DocumentVerificationCard
                key={doc.id}
                document={doc}
                onVerify={() => verifyDocument(doc.id)}
                onReject={() => rejectDocument(doc.id)}
              />
            ))}
          </TabsContent>
        </Tabs>
      </Card>
      
      {/* Verification Checklist */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Verification Checklist</h3>
        
        <div className="space-y-3">
          <ChecklistItem
            label="Business registration verified"
            checked={vendor.businessVerified}
          />
          <ChecklistItem
            label="Insurance coverage adequate ($1M minimum)"
            checked={vendor.insuranceVerified}
          />
          <ChecklistItem
            label="Required licenses valid"
            checked={vendor.licensesVerified}
          />
          <ChecklistItem
            label="Background check clear"
            checked={vendor.backgroundVerified}
          />
          <ChecklistItem
            label="Service area confirmed"
            checked={vendor.serviceAreaVerified}
          />
        </div>
      </Card>
      
      {/* Admin Notes */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Verification Notes</h3>
        
        <textarea
          className="w-full h-32 p-3 border rounded"
          placeholder="Add notes about this verification..."
          value={verificationNotes}
          onChange={(e) => setVerificationNotes(e.target.value)}
        />
      </Card>
      
      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => window.location.href = '/vendors/pending'}
        >
          Back to Queue
        </Button>
        
        <div className="flex gap-4">
          <Button
            variant="destructive"
            onClick={() => setShowRejectDialog(true)}
          >
            Reject Application
          </Button>
          
          <Button
            variant="outline"
            onClick={() => setShowMoreInfoDialog(true)}
          >
            Request More Info
          </Button>
          
          <Button
            onClick={handleApprove}
            disabled={!vendor.allDocumentsVerified}
          >
            Approve Vendor
          </Button>
        </div>
      </div>
    </div>
  )
}

function DocumentVerificationCard({ document, onVerify, onReject }) {
  const [viewing, setViewing] = useState(false)
  
  return (
    <div className="border rounded p-4 mb-4">
      <div className="flex justify-between items-start">
        <div>
          <h4 className="font-medium">{document.name}</h4>
          <p className="text-sm text-gray-500">
            Expires: {new Date(document.expiryDate).toLocaleDateString()}
          </p>
          {document.verified && (
            <Badge variant="success" className="mt-2">Verified</Badge>
          )}
        </div>
        
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setViewing(true)}
          >
            View
          </Button>
          
          {!document.verified && (
            <>
              <Button
                size="sm"
                variant="success"
                onClick={onVerify}
              >
                Verify
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={onReject}
              >
                Reject
              </Button>
            </>
          )}
        </div>
      </div>
      
      {viewing && (
        <DocumentViewer
          url={document.url}
          onClose={() => setViewing(false)}
        />
      )}
    </div>
  )
}
```

### 3. Dispute Resolution System

```tsx
// apps/admin-dashboard/src/components/disputes/DisputeDetail.tsx

import { useState } from 'react'
import { Card } from '@clear-quote-pro/ui/card'
import { Button } from '@clear-quote-pro/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@clear-quote-pro/ui/tabs'
import { Timeline } from '../shared/Timeline'
import { useDisputeStore } from '../../stores/disputes.store'

export function DisputeDetail({ disputeId }: { disputeId: string }) {
  const { dispute, resolveDispute } = useDisputeStore()
  const [resolution, setResolution] = useState({
    type: '',
    refundAmount: 0,
    notes: ''
  })
  
  const handleResolve = async () => {
    try {
      await resolveDispute(disputeId, resolution)
      
      // Notify both parties
      await Promise.all([
        notifyCustomer(dispute.customerId, resolution),
        notifyVendor(dispute.vendorId, resolution)
      ])
      
      toast({
        title: 'Dispute Resolved',
        description: 'Both parties have been notified',
        variant: 'success'
      })
      
      window.location.href = '/disputes'
    } catch (error) {
      toast({
        title: 'Resolution Failed',
        description: error.message,
        variant: 'error'
      })
    }
  }
  
  return (
    <div className="space-y-6">
      {/* Dispute Header */}
      <Card className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold">Dispute #{dispute.id}</h2>
            <p className="text-gray-600">{dispute.category} • {dispute.projectType}</p>
          </div>
          <Badge variant={getPriorityVariant(dispute.priority)}>
            {dispute.priority} Priority
          </Badge>
        </div>
        
        <div className="grid grid-cols-3 gap-4 mt-4">
          <div>
            <p className="text-sm text-gray-500">Amount</p>
            <p className="text-xl font-semibold">${dispute.amount}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Opened</p>
            <p className="font-medium">{formatDate(dispute.createdAt)}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Status</p>
            <Badge>{dispute.status}</Badge>
          </div>
        </div>
      </Card>
      
      {/* Parties Information */}
      <div className="grid grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="font-semibold mb-3">Customer</h3>
          <p className="font-medium">{dispute.customerName}</p>
          <p className="text-sm text-gray-600">{dispute.customerEmail}</p>
          <p className="text-sm text-gray-600">{dispute.customerPhone}</p>
          <p className="text-sm mt-2">
            Rating: ⭐ {dispute.customerRating}/5.0
          </p>
        </Card>
        
        <Card className="p-6">
          <h3 className="font-semibold mb-3">Vendor</h3>
          <p className="font-medium">{dispute.vendorName}</p>
          <p className="text-sm text-gray-600">{dispute.vendorEmail}</p>
          <p className="text-sm text-gray-600">{dispute.vendorPhone}</p>
          <p className="text-sm mt-2">
            Rating: ⭐ {dispute.vendorRating}/5.0
          </p>
        </Card>
      </div>
      
      {/* Dispute Details */}
      <Card className="p-6">
        <Tabs defaultValue="statements">
          <TabsList>
            <TabsTrigger value="statements">Statements</TabsTrigger>
            <TabsTrigger value="evidence">Evidence</TabsTrigger>
            <TabsTrigger value="messages">Messages</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
          </TabsList>
          
          <TabsContent value="statements" className="mt-4 space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Customer Statement</h4>
              <p className="p-4 bg-gray-50 rounded">
                {dispute.customerStatement}
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Vendor Response</h4>
              <p className="p-4 bg-gray-50 rounded">
                {dispute.vendorResponse || 'No response yet'}
              </p>
            </div>
          </TabsContent>
          
          <TabsContent value="evidence" className="mt-4">
            <div className="grid grid-cols-3 gap-4">
              {dispute.evidence?.photos?.map((photo, index) => (
                <img
                  key={index}
                  src={photo}
                  alt={`Evidence ${index + 1}`}
                  className="w-full h-32 object-cover rounded cursor-pointer"
                  onClick={() => openImageViewer(photo)}
                />
              ))}
            </div>
            
            <div className="mt-4">
              <h4 className="font-semibold mb-2">Documents</h4>
              {dispute.evidence?.documents?.map((doc, index) => (
                <a
                  key={index}
                  href={doc}
                  target="_blank"
                  className="block p-2 hover:bg-gray-50"
                >
                  📄 Document {index + 1}
                </a>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="messages" className="mt-4">
            <MessageThread messages={dispute.messages} />
          </TabsContent>
          
          <TabsContent value="timeline" className="mt-4">
            <Timeline events={dispute.timeline} />
          </TabsContent>
        </Tabs>
      </Card>
      
      {/* Resolution Options */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Resolution Options</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block font-medium mb-2">Resolution Type</label>
            <select
              className="w-full p-2 border rounded"
              value={resolution.type}
              onChange={(e) => setResolution({...resolution, type: e.target.value})}
            >
              <option value="">Select resolution...</option>
              <option value="full_refund">Full Refund to Customer</option>
              <option value="partial_refund">Partial Refund</option>
              <option value="vendor_complete">Vendor to Complete Work</option>
              <option value="new_vendor">Assign New Vendor</option>
              <option value="no_action">No Action Required</option>
            </select>
          </div>
          
          {resolution.type === 'partial_refund' && (
            <div>
              <label className="block font-medium mb-2">Refund Amount</label>
              <input
                type="number"
                className="w-full p-2 border rounded"
                value={resolution.refundAmount}
                onChange={(e) => setResolution({
                  ...resolution, 
                  refundAmount: parseFloat(e.target.value)
                })}
              />
            </div>
          )}
          
          <div>
            <label className="block font-medium mb-2">Resolution Notes</label>
            <textarea
              className="w-full h-32 p-2 border rounded"
              value={resolution.notes}
              onChange={(e) => setResolution({...resolution, notes: e.target.value})}
              placeholder="Explain the resolution decision..."
            />
          </div>
        </div>
      </Card>
      
      {/* Actions */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => window.location.href = '/disputes'}
        >
          Back to Disputes
        </Button>
        
        <div className="flex gap-4">
          <Button variant="outline" onClick={() => contactParty('customer')}>
            Contact Customer
          </Button>
          <Button variant="outline" onClick={() => contactParty('vendor')}>
            Contact Vendor
          </Button>
          <Button
            onClick={handleResolve}
            disabled={!resolution.type}
          >
            Resolve Dispute
          </Button>
        </div>
      </div>
    </div>
  )
}
```

### 4. Analytics Dashboard

```tsx
// apps/admin-dashboard/src/components/analytics/AnalyticsDashboard.tsx

import { Card } from '@clear-quote-pro/ui/card'
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar,
  PieChart,
  Pie,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend,
  ResponsiveContainer 
} from 'recharts'
import { useAnalyticsStore } from '../../stores/analytics.store'

export function AnalyticsDashboard() {
  const { metrics, revenue, userGrowth, serviceBreakdown } = useAnalyticsStore()
  
  return (
    <div className="space-y-6">
      {/* KPI Grid */}
      <div className="grid grid-cols-4 gap-4">
        <KPICard
          title="Total Revenue"
          value={`$${metrics.totalRevenue.toLocaleString()}`}
          change="+23%"
          trend="up"
        />
        <KPICard
          title="Active Users"
          value={metrics.activeUsers.toLocaleString()}
          change="+18%"
          trend="up"
        />
        <KPICard
          title="Completion Rate"
          value={`${metrics.completionRate}%`}
          change="+5%"
          trend="up"
        />
        <KPICard
          title="Avg Rating"
          value={`${metrics.avgRating}/5.0`}
          change="+0.2"
          trend="up"
        />
      </div>
      
      {/* Revenue Chart */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Revenue Trend</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={revenue}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="revenue" 
              stroke="#3b82f6" 
              name="Revenue"
            />
            <Line 
              type="monotone" 
              dataKey="fees" 
              stroke="#10b981" 
              name="Platform Fees"
            />
          </LineChart>
        </ResponsiveContainer>
      </Card>
      
      {/* Service Performance */}
      <div className="grid grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Jobs by Service</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={serviceBreakdown}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="service" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="jobs" fill="#3b82f6" />
            </BarChart>
          </ResponsiveContainer>
        </Card>
        
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Revenue by Service</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={serviceBreakdown}
                dataKey="revenue"
                nameKey="service"
                cx="50%"
                cy="50%"
                outerRadius={100}
                fill="#3b82f6"
                label
              />
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </Card>
      </div>
      
      {/* User Growth */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">User Growth</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={userGrowth}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="customers" 
              stroke="#3b82f6" 
              name="Customers"
            />
            <Line 
              type="monotone" 
              dataKey="vendors" 
              stroke="#10b981" 
              name="Vendors"
            />
          </LineChart>
        </ResponsiveContainer>
      </Card>
    </div>
  )
}
```

### 5. Real-time Monitoring

```typescript
// apps/admin-dashboard/src/services/monitoring.ts

export class SystemMonitor {
  private ws: WebSocket | null = null
  
  connect() {
    this.ws = new WebSocket('wss://api.clearquotepro.com/admin/monitor')
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleSystemEvent(data)
    }
  }
  
  private handleSystemEvent(event: any) {
    switch (event.type) {
      case 'vendor_signup':
        this.notifyAdmins('New vendor signup', event.data)
        break
        
      case 'dispute_opened':
        this.notifyAdmins('New dispute', event.data, 'urgent')
        break
        
      case 'document_expired':
        this.notifyAdmins('Document expired', event.data, 'warning')
        break
        
      case 'high_value_job':
        this.notifyAdmins('High value job', event.data, 'info')
        break
        
      case 'system_error':
        this.notifyAdmins('System error', event.data, 'critical')
        break
    }
  }
  
  private notifyAdmins(title: string, data: any, priority = 'normal') {
    // Update notification store
    useNotificationStore.getState().addNotification({
      title,
      data,
      priority,
      timestamp: new Date()
    })
    
    // Show toast for urgent items
    if (priority === 'urgent' || priority === 'critical') {
      toast({
        title,
        description: data.description,
        variant: priority === 'critical' ? 'destructive' : 'warning'
      })
    }
    
    // Play sound for critical items
    if (priority === 'critical') {
      this.playAlertSound()
    }
  }
  
  private playAlertSound() {
    const audio = new Audio('/sounds/alert.mp3')
    audio.play()
  }
}
```

## API Routes

### Vendor Approval API

```typescript
// apps/admin-dashboard/src/pages/api/vendors/approve.ts

import type { APIRoute } from 'astro'
import { db } from '../../../lib/db'
import { vendors, vendorDocuments, auditLog } from '@clear-quote-pro/database/schema'
import { z } from 'zod'

const approveVendorSchema = z.object({
  vendorId: z.string().uuid(),
  notes: z.string().optional(),
  verifiedDocuments: z.array(z.string().uuid())
})

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const admin = locals.admin
    if (!hasPermission(admin, 'vendors.manage')) {
      return new Response('Forbidden', { status: 403 })
    }
    
    const body = await request.json()
    const data = approveVendorSchema.parse(body)
    
    // Start transaction
    await db.transaction(async (tx) => {
      // Update vendor status
      await tx
        .update(vendors)
        .set({
          status: 'active',
          verifiedAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(vendors.id, data.vendorId))
      
      // Mark documents as verified
      for (const docId of data.verifiedDocuments) {
        await tx
          .update(vendorDocuments)
          .set({
            status: 'verified',
            verifiedBy: admin.id,
            verifiedAt: new Date()
          })
          .where(eq(vendorDocuments.id, docId))
      }
      
      // Create audit log
      await tx.insert(auditLog).values({
        adminId: admin.id,
        actionType: 'vendor_approved',
        entityType: 'vendor',
        entityId: data.vendorId,
        description: `Vendor approved by ${admin.name}`,
        metadata: { notes: data.notes },
        ipAddress: request.headers.get('x-forwarded-for'),
        userAgent: request.headers.get('user-agent')
      })
    })
    
    // Send notification to vendor
    await sendVendorNotification(data.vendorId, {
      type: 'approval',
      title: 'Your vendor account has been approved!',
      message: 'You can now start accepting jobs on Clear Quote Pro.'
    })
    
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('Vendor approval failed:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
}
```

## Environment Variables

```bash
# .env.local

# Database
DATABASE_URL="postgresql://user:password@localhost:5432/clearquotepro"

# Auth
AUTH_SECRET="your-admin-auth-secret"
TWO_FACTOR_SECRET="your-2fa-secret"

# Admin Settings
ADMIN_SESSION_DURATION="28800" # 8 hours
ADMIN_IP_WHITELIST="***********/24,10.0.0.0/8"

# Monitoring
SENTRY_DSN="your-sentry-dsn"
MONITORING_WEBHOOK="https://your-monitoring-webhook"

# Analytics
ANALYTICS_API_KEY="your-analytics-key"

# Email
RESEND_API_KEY="your-resend-key"
ADMIN_EMAIL="<EMAIL>"

# Public URLs
PUBLIC_APP_URL="http://localhost:4324"
PUBLIC_VENDOR_URL="http://localhost:4323"
PUBLIC_HOMEOWNER_URL="http://localhost:4322"
```

## Security Measures

### Admin-Specific Security
- Two-factor authentication required
- IP whitelisting for admin access
- Session timeout after inactivity
- Audit logging for all actions
- Role-based access control
- Encrypted sensitive data viewing

## Deployment

### Production Considerations
- Separate admin subdomain (admin.clearquotepro.com)
- Enhanced DDoS protection
- Database read replicas for analytics
- Backup admin interface
- Incident response procedures
- 24/7 monitoring alerts

## Performance Optimization

### Caching Strategy
- Redis for session management
- Query result caching
- Dashboard metric caching (5 min TTL)
- Static asset CDN

### Database Optimization
- Indexed queries for search
- Materialized views for analytics
- Partitioned tables for audit logs
- Connection pooling

## Testing

### Admin-Specific Tests
```typescript
// tests/admin/vendor-approval.test.ts
describe('Vendor Approval', () => {
  it('should approve vendor with all documents', async () => {
    // Test approval flow
  })
  
  it('should prevent approval without permissions', async () => {
    // Test permission checking
  })
})
```

## Monitoring & Alerts

### Critical Alerts
- New high-priority dispute
- Document expiration < 7 days
- Unusual transaction patterns
- System errors
- Failed login attempts
- Vendor suspension events