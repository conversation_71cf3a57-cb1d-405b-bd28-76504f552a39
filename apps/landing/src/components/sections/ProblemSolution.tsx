import { X, Check } from 'lucide-react';

export default function ProblemSolution() {
  const problems = [
    "Calling multiple contractors for quotes",
    "Wondering if you're being overcharged",
    "Taking time off work for estimates",
    "Dealing with no-shows and delays",
    "Surprise costs and hidden fees"
  ];
  
  const solutions = [
    "Instant pricing for any project",
    "Pre-negotiated, transparent rates",
    "Book online in minutes",
    "Guaranteed appointment times",
    "Fixed prices, no surprises"
  ];
  
  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto">
          {/* Section header */}
          <div className="text-center mb-16">
            <h2 className="text-gray-900 mb-4">
              The Old Way vs. The Clear Way
            </h2>
            <p className="text-xl text-gray-600">
              Stop wasting time. Start getting things done.
            </p>
          </div>
          
          {/* Comparison grid */}
          <div className="grid md:grid-cols-2 gap-8 md:gap-12">
            {/* The Old Way */}
            <div className="relative">
              <div className="absolute -top-3 -left-3 text-xs uppercase tracking-wider text-gray-400 font-medium">
                The Old Way
              </div>
              <div className="bg-gray-50 rounded-2xl p-8 pt-10">
                <div className="space-y-4">
                  {problems.map((problem, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="mt-1 flex-shrink-0">
                        <X className="w-5 h-5 text-gray-400" />
                      </div>
                      <p className="text-gray-600">{problem}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* The Clear Way */}
            <div className="relative">
              <div className="absolute -top-3 -left-3 text-xs uppercase tracking-wider text-gray-900 font-medium">
                The Clear Way
              </div>
              <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl p-8 pt-10 text-white">
                <div className="space-y-4">
                  {solutions.map((solution, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="mt-1 flex-shrink-0">
                        <Check className="w-5 h-5 text-white" />
                      </div>
                      <p className="text-gray-100">{solution}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          {/* Bottom message */}
          <div className="mt-16 text-center">
            <p className="text-lg text-gray-600 mb-2">
              Join thousands who've discovered a better way to handle home repairs.
            </p>
            <p className="text-2xl font-semibold text-gray-900">
              Save 30% on average. Save 100% of the hassle.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}