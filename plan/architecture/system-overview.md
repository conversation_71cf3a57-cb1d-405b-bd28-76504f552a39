# [Homeowner Control App]

## Overview

An application that would allow Homeowners to select from an ever-growing list of pre negotiated prices for work. Homeowner would be able to post a job and vendors would be able to pickup and schedule the job. Homeowners would have visibility into vendors licensing and current insurance level, along with a vendor score card base upon previous jobs completed and Homeowner satisfaction, vendors would also be able to rate Homeowners, think Uber and Air-B-N-B. Integrate Sibi Pro's API into the application for owners to select materials for the job, this would increase or decrease the pricing, could also give owners the option to pay for materials themselves. Propose a 20% markup on all costs, this is where the platform can make money.

## Application

• Owner signs up for the platform
  o Information to be collected
    ▪ Name
    ▪ Address
    ▪ Phone number (verify via texted code)
    ▪ Email (verify by emailed code)
    ▪ If they are the owner

## Vendors

• Vendors can sign up for the platform
  o Information to be collected
    ▪ Vendor Name
    ▪ Company Name
    ▪ Phone number (verify via texted code)
    ▪ Email (verify by emailed code)
    ▪ Address (verify by mailed code)
    ▪ 1099 information
    ▪ TIN information
    ▪ Licensing information
    ▪ Services offered
      • Does the service they selected need licensing? Does the vendor hold a current license?
    ▪ Insurance information COI
    ▪ Areas the vendor can accept work from
    ▪ Is the person signing up the owner or do they represent the owner?
  o Vendors would have to sign an agreement that all transactions would be conducted on platform
  o Any change orders would need to be added on platform
    ▪ To be reviewed by system

• Insurance
  o System should be set up to automatically check the COI information and 60 days before expiration start reaching out to the vendor to have them upload the new documentation.
  o If the vendors COI, licensing, or other information expires then they are automatically suspended from the homeowner's visibility to select and schedule with the vendors.
    ▪ This should incentivize the vendors to keep their information up to date because they will not be able to receive work or accept work on the platform
    ▪ If the vendors information is expired the only selections they should be able to make on the platform should be to update their information

## Services

• Painting
  o Walls only - $1.50/sq {material plus labor}
  o Walls and Trim/Doors - $1.75/sqft {material plus labor}
  o Walls, Trim/Doors, and Ceilings - $2.00/sqft {material plus labor}
• Flooring
  o LVP
    ▪
  o Wood
    ▪
  o Tile
    ▪
  o Carpet
    ▪
• Plumbing
  o Water heaters
  o Toilet
  o Sinks
  o
• HVAC
  o Repairs
  o Replacement
  o Servicing
  o
• Electrical
  o Repairs
  o Replacement
    ▪ Pannel
    ▪ Sub Pannel
    ▪ Disconnect
  o Servicing
  o
• Roofing
  o Repairs
  o New roof
• Landscaping

## Rating Systems

• Owners could rate the work product of the vendor
  o Timeliness of the vendor
  o Vendor responsiveness
  o Scheduling
  o Finished project satisfaction
• Vendors could rate the owners
  o Was the owner easy to work with
  o How did the owner treat the crew onsite?
  o Did the owner change the schedule more than 1 time
  o

## Sibi Pro API Integration

• Sibi has most sqft for most residential homes in the country
• Material pricing discounts
• Owners could select the materials they want to use for the project
  o Only available for specific projects due to some projects like HVAC replacements being too complicated
  o Paint, flooring, appliances,
  o Price for the project will change based upon materials selected.
    ▪ Running tally of cost should be displayed on the side bar
    ▪ Cost projections should be displayed by each option, clarity should be our goal
    ▪ API Token: <add-your-token-here>

## Payment by Owners

• Owners will need to pay at least 50% before scheduling can occur
• Additional 25% should be due the day before the start of the project
• Final 25% due upon confirmation that project has completed
• Look into getting an affirm or similar option for payments

## Scheduling

• Once a vendor has accepted the work an email/push notification, text message would be sent to the homeowner to schedule their appointment with the vendor
• Vendor would post their availability when accepting the job, could also link their availability to a scheduling application or have the option to input their availability
• Some jobs may need to be quoted due to size of scope of work, in this case the resident would pay a fee of $75 for the vendor to come and scope the job. If the owner likes the final price and would like to move forward with scheduling the vendor the quotation fee would be deducted from the final project cost.

## Company Name Possibilities

### Professional & Trust-Oriented
TrueHome Connect
Verified Home Pros
TrustBuilt
SafeFix Network
HomePro Verified
BuiltRight
Marketplace

### 💡 Modern & Tech-Forward
FixFlow
UpNest Pro
RenovaLink
RevoHome
TaskGrid
CraftNest

### 🏠 Homeowner-Centric
Your Project Hub
My Home Works
Control My Fix
My Trusted Pros
OwnerFix
ProjectInHand

### 💰 Price-Transparency Focused
ClearQuote Pro
PriceRight Home
FlatRate Fix
FairBid Pros
SmartScope

### 🛠️ Marketplace-Themed
Service Match Hub
VendorVista
FixFast Marketplace
Contractor Cloud
Home Job Match

### 🧱 Material Selection Focused (with Sibi Integration)
PickBuild
MatMatch
Style and Scope
BuildByChoice
SpecSelect Pro

## Description Ideas

1. Pre-negotiated pricing for Homeowners and jobs for contractors.
2. Pre-negotiated and clear pricing for Homeowners. Pre scoped and bid jobs for contractors.

## Workflow

### Homeowner

1. Sign in/Account Creation
2. First time user - Homeowner
   a. Information to be collected
      i. Name
      ii. Address
      iii. Phone Number
      iv. Email Address
      v. Password
   b. Information to be verified
      i. Phone Number
         1. Send text or phone call with verification code
      ii. Email
         1. Send email with verification code
3. Main Page for Homeowners
   a. Start a new project [consider other names than project]
   b. Current projects
   c. Finished projects
   d. Saved projects
   e. My property
      i. List of homes by address, should be ordered with the primary residents first
4. Starting a new project
   a. Project type
   b. Appliance
   c. Cabinets
   d. Cleaning
   e. Countertops
   f. Driveway
   g. Electrical*
   h. Fencing
   i. Flooring
   j. Foundation*
   k. Flooring
   l. Garage
   m. General/Residential Contractor*
   n. Handyman
   o. Hardscaping
   p. HVAC*
   q. Landscaping
   r. Paining
   s. Plumbing*
   t. Pool
   u. Roofing
   v. Siding
   w. Windows
5. New project workflow example
   i. Select Start a new project
   ii. Select which property this project is for
   iii. Select project type
   iv. Painting project
      1. Number of rooms or whole home painting?
         a. Let Homeowner enter an integer
         b. Or select whole home
      2. Selected number of rooms
         a. Ask Homeowner to measure each room
            i. Give boxes "Room One" make editable so Homeowner can name each room if the choose
            ii. Two boxes beneath each Room box and a camera button
               1. Length
               2. Width
               3. Take a few photos of each room
            iii. Future need look into measuring rooms base on photos
         b. Calculate total square footage of the project
            i. If greater than 50% of the homes total square ask if the Homeowner would rather paint the whole home due to painting more than 50% of the home.
      3. Are any walls greater than 8 feet?
         a. No, move on
         b. Yes
            i. Which rooms are greater than 8 feet
               1. Need to get photos of each room from floor to ceiling
               2. Include any previously built out rooms and photos
      4. What is the Homeowner wanting painted?
         a. Walls Only
         b. Walls, doors and trim
         c. Walls, doors and trim, and ceilings
      5. Go with our recommendation on paint finish?
         a. Yes (this is the recommended finish)
            i. Eggshell on walls
            ii. Flat on ceilings
            iii. Semi-Gloss on trim and doors
         b. No
            i. Pick your wall finish
            ii. Pick your trim and door finish
            iii. Pick your ceiling finish
      6. Pick your paint
         a. Walls with Eggshell finish
            i. Speedhide Pro EV Zero – Economical ~$0.18/sqft
            ii. Speedhide Zero VOC – Mid Range ~$0.40/sqft
            iii. UltraLast – Best ~$.50/sqft
         b. Ceilings with Flat finish
            i. Pre-Mixed Gypsum Speedhide Pro EV Zero – Economical ~$0.03/sqft
            ii. Speedhide Zero VOC – Mid Range ~$0.06/sqft
            iii. UltraLast – Best ~$0.10/sqft
         c. Doors and Trim with Semi-Gloss finish
            i. Speedhide Pro EV Zero – Economical ~$0.02
            ii. Speedhide Zero VOC – Mid Range ~$0.04
            iii. UltraLast – Best ~$0.05
      7. Choose your color
         a. Paint is a very personal choice if you are unsure which color works best for your home. Find your local PPG store and choose your paint swatches.
         b. Walls
            i. Whites
            ii. Greys
            iii. Blacks
            iv. Beiges
            v. Neutrals
            vi. Metallics
            vii. Blues
            viii. Aquas
            ix. Greens
            x. Yellows
            xi. Oranges
            xii. Pinks
            xiii. Reds
            xiv. Purples
            xv. More
         c. Doors and Trim
            i. Whites
            ii. Greys
            iii. Blacks
            iv. Beiges
            v. Neutrals
            vi. Metallics
            vii. Blues
            viii. Aquas
            ix. Greens
            x. Yellows
            xi. Oranges
            xii. Pinks
            xiii. Reds
            xiv. Purples
            xv. More
         d. Ceilings
            i. Whites
            ii. Greys
            iii. Blacks
            iv. Beiges
            v. Neutrals
            vi. Metallics
            vii. Blues
            viii. Aquas
            ix. Greens
            x. Yellows
            xi. Oranges
            xii. Pinks
            xiii. Reds
            xiv. Purples
            xv. More
      8. Project Scope
         a. Subtotal for labor
         b. Subtotal for materials
         c. Total for labor and materials
         d. Cost/square foot
         e. Approximate number of days for completed project
      9. Schedule your project
         a. When would you like your project to start by
            i. Within the next 30 days
            ii. Within the next 60 days
            iii. Within the next 90 days
            iv. Next two weeks (extra fees)

### Vendor

1. Sign in/Account Creation
2. First time user - Contractor
   a. Information to be collected to create an account
      i. Name
      ii. Business Name
      iii. Address
      iv. Phone Number
      v. Email Address
      vi. Password
   b. Information to be verified
      i. Phone Number
         1. Send text or phone call with verification code
      ii. Email
         1. Send email with verification code
   c. Insurance
      i. We require all contractors to be insured...
      ii. Upload a copy of your Certificate of Insurance (COI)
      iii. If you would like to be put in contact with a recommended insurance provider, click here...
   d. What trades do you provide services for
      i. Appliance
      ii. Cabinets
      iii. Cleaning
      iv. Countertops
      v. Driveway
      vi. Electrical*
      vii. Fencing
      viii. Flooring
      ix. Foundation*
      x. Flooring
      xi. Garage
      xii. General/Residential Contractor*
      xiii. Handyman
      xiv. Hardscaping
      xv. HVAC*
      xvi. Landscaping
      xvii. Paining
      xviii. Plumbing*
      xix. Pool
      xx. Roofing
      xxi. Siding
      xxii. Windows
   e. Sub-Trades
      i. Appliance
         1. Repair
         2. Installation
      ii. Cabinets
         1. Repairs
         2. Replacement
      iii. Cleaning
         1. Carpet
         2. Deep cleaning
         3. Reoccurring cleaning
      iv. Countertops
         1. Repairs
         2. Refinishing
         3. Replacement
      v. Driveway
         1. Repairs
         2. Replacement
      vi. Electrical
         1. Inspection
         2. Electrical Meter
         3. Light fixtures
         4. Outlets
         5. Panel/Breakers
         6. Rewire
         7. Low Voltage
      vii. Fencing
         1. Repair
         2. Replacement
            a. Chain Link
            b. Vinyl
            c. Wood
            d. Gate
         3. Demo
      viii. Flooring
         1. Installation
            a. Carpet
            b. LVP
            c. Tile
            d. Wood
            e. Glue Down LVP
         2. Repair
            a. Carpet
            b. LVP
            c. Tile
            d. Wood
            e. Glue Down LVP
            f. Sub-floor
      ix. Foundation
         1. Inspection
         2. Repairs
         3. Vapor Barrier – crawl space
      x. Garage
         1. Installation
            a. Door
            b. Motor
            c. Floor Painting/Sealing
      xi. General/Residential Contractor
      xii. Gutters
         1. Cleaning
            a. 1 story
            b. 2 story
         2. Repairs
         3. Replacement
      xiii. Handyman
         1.
      xiv. Hardscaping
         1. Deck
         2. Patio
         3. Walkway/path
         4. Retaining wall
      xv. HVAC
         1. Repairs
         2. Replacement
         3. Reoccurring Servicing
         4. Inspection
      xvi. Landscaping
         1. Mowing
            a. One time
            b. Reoccurring
         2. Leaf removal
         3. Trash removal
         4. Irrigation service
         5. Mulch
         6. Tree removal/trimming
         7. Stump Grinding
      xvii. Paining
         1. Drywall repairs
         2. Interior Painting
         3. Exterior Painting
      xviii. Plumbing
         1. Inspection
         2. Repairs
         3. Replacement
         4. Water Heaters
         5.
      xix. Pool
         1. Maintenance
         2. Inspection
         3. Reoccurring maintenance
      xx. Roofing
         1. Repairs
         2. Inspection
         3. Replacement
         4.
      xxi. Siding
         1. Repairs
         2. Replacement
      xxii. Windows
         1. Repairs
         2. Replacement
   f. Additional Information
      i. TIN
      ii. Additional Points of Contact
         1. Dispatcher
            a. Name
            b. Phone Number
            c. Email
         2. Accounting
            a. Name
            b. Phone Number
            c. Email
         3. Scheduling
            a. Name
            b. Phone Number
            c. Email
         4. Ownership/CEO/President
            a. Name
            b. Phone Number
            c. Email
      iii. Payment Information
         1. EFT
         2. Wire
         3. Check
      iv. License Verification
         1. Upload all licenses (must have license for HVAC, Electrical, Plumbing, and Residential and General Contractor
      v. Company Website
      vi. Coverage area
         1. MSA
            a. What MSA's does the vendor cover?
            b. Do they cover the entire MSA or only part?
               i. If all give list of counties with Cities for verification
               ii. If no, then move to 2
         2. County
            a. Give list of counties within MSA's that the vendor covers
            b. Verify with list of Cities within the MSA
            c. Do they cover the entire County?
               i. If no, move to question 3
         3. City
            a. Give list of Cities within covered County's
               i. Verify that the vendor covers the individual cities.
         4. Zip Codes
3. Vendor proposal reception/acceptance
   a. Customer submits a proposal of work for vendor review
      i. All vendors who are registered in the area will receive a notification that they have a new job available in their area
         1. Push notification
         2. Text message
         3. Email
      ii. The vendor will review the proposal of work, proposal will have a breakdown of the proposed scope of work, labor, any materials the vendor needs to purchase, and time frame the homeowner would like the services started by. Vendors will have 48 hours to accept the proposal.
         1. Vendor Accepts
            a. Vendors acceptance placed into a queue and after the 48-hour period all acceptances submitted to homeowner
         2. Vendor rejects
            a. No notifications needed

### Proposal workflow after vendor acceptance

1. 48 hours after proposal sent to all vendors
   a. Homeowners will receive notification that they have vendors who have accepted the proposed scope of work
   b. Homeowners will review vendors who have accepted the proposed scope of work
      i. Sorting options should be available for the homeowner
         1. Rating
         2. Number of jobs completed
   c. Homeowner selects vendor
      i. Notification sent to vendor that they were selected for the job
      ii. Homeowners and vendors will coordinate schedules to find the optimal start date for the project
      iii. When scheduling is agreed upon before the homeowner can lock in the schedule, they will need to fund at least 50% of the job cost.
   d. 24 hours before the job is set to begin the homeowner will need to fund at least 25% of the job cost.

### Job start to finish

1. Day 1 of job
   a. The vendor will need to take before photos of the project, at least 3 per room.
2. Final day of job
   a. The vendor will need to take final photos of all rooms to mark the job as completed.
   b. The homeowner will receive notification that the vendor has marked the job as completed. Homeowner will confirm.
      i. Confirmed
         1. Homeowners will pay the final 25% of job cost.
      ii. Not confirmed
         1. Homeowners will need to submit photos and a written statement as to why they feel the job was not completed.
   c. Homeowners and vendors are sent a survey to rate their experience
      i. Homeowners' questions
         1. How was your experience with [vendor name]
         2. How was your experience scheduling with [vendor name]
         3. How would you rate the workmanship of [vendor name]
         4. How would you rate the professionalism of [vendor name]
         5. How was your overall experience with the process?
      ii. Vendor's questions
         1. How was your experience with [Homeowner]
         2. How was your experience scheduling with [Homeowner]
         3. How was your overall experience with the process?
3. 48 hours after project completion
   a. Vendor paid for completed work