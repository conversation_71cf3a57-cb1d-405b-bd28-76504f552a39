export default function Footer() {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-white border-t border-gray-100">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="max-w-6xl mx-auto">
          {/* Main footer content */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
            {/* Product */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Product</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">How It Works</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Services</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Pricing</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Coverage Areas</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">For Contractors</a></li>
              </ul>
            </div>
            
            {/* Company */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Company</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">About Us</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Careers</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Press</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Blog</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Contact</a></li>
              </ul>
            </div>
            
            {/* Resources */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Resources</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Help Center</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Homeowner Guide</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Landlord Resources</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Contractor Portal</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">API Docs</a></li>
              </ul>
            </div>
            
            {/* Legal */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Legal</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Terms of Service</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Insurance Info</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Guarantee Terms</a></li>
                <li><a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Contractor Agreement</a></li>
              </ul>
            </div>
          </div>
          
          {/* Bottom section */}
          <div className="pt-8 border-t border-gray-100">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              {/* Logo and copyright */}
              <div className="flex items-center gap-8">
                <div className="font-bold text-xl text-gray-900">Clear Quote Pro</div>
                <p className="text-gray-600 text-sm">
                  © {currentYear} Clear Quote Pro. All rights reserved.
                </p>
              </div>
              
              {/* Contact info */}
              <div className="flex items-center gap-6 text-sm text-gray-600">
                <a href="mailto:<EMAIL>" className="hover:text-gray-900 transition-colors">
                  <EMAIL>
                </a>
                <span className="text-gray-300">|</span>
                <a href="tel:1-800-XXX-XXXX" className="hover:text-gray-900 transition-colors">
                  1-800-XXX-XXXX
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}