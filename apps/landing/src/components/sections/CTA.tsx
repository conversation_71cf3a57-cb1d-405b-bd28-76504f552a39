import { But<PERSON> } from '@repo/ui/button';
import { ArrowRight } from 'lucide-react';

export default function CTA() {
  return (
    <section className="py-24 bg-gradient-to-br from-gray-900 to-gray-800">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          {/* Main message */}
          <h2 className="text-white mb-6">
            Ready to Stop Wasting Time on Home Repairs?
          </h2>
          
          <p className="text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed">
            Join 2,500+ property owners who've discovered a better way. 
            Get instant pricing, vetted pros, and guaranteed satisfaction.
          </p>
          
          {/* CTAs */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Button 
              size="lg" 
              className="text-base px-8 py-6 bg-white text-gray-900 hover:bg-gray-100"
            >
              Get Started - It's Free
              <ArrowRight className="ml-2 w-4 h-4" />
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="text-base px-8 py-6 border-white/20 text-white hover:bg-white/10"
            >
              Schedule a Demo
            </Button>
          </div>
          
          {/* Trust text */}
          <p className="text-gray-400 text-sm">
            No credit card required. 60-day money-back guarantee.
          </p>
          
          {/* Early access offer */}
          <div className="mt-16 p-6 bg-white/10 backdrop-blur rounded-2xl inline-block">
            <p className="text-white font-semibold mb-1">
              Limited Time: Early Access Offer
            </p>
            <p className="text-gray-300">
              Join now and get 20% off your first three projects
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}