import { Card } from '@repo/ui/card';
import { Star } from 'lucide-react';

const testimonials = [
  {
    name: "<PERSON>",
    role: "Property Investor",
    content: "I manage 12 rental properties. Clear Quote Pro saved me 15 hours a month and $30,000 a year on repairs. The tax documentation alone is worth it.",
    rating: 5,
    properties: "12 properties"
  },
  {
    name: "<PERSON>",
    role: "Homeowner",
    content: "Finally, no more calling around for quotes. I got my bathroom renovated 40% cheaper than my neighbor who used traditional contractors.",
    rating: 5,
    location: "Seattle, WA"
  },
  {
    name: "<PERSON>",
    role: "Property Manager",
    content: "My tenants love how quickly repairs get done now. I love that I can manage everything from my phone. Game changer for property management.",
    rating: 5,
    properties: "28 units"
  },
  {
    name: "<PERSON>",
    role: "First-time Homeowner",
    content: "As a new homeowner, I had no idea what repairs should cost. Clear Quote gives me confidence I'm not being taken advantage of.",
    rating: 5,
    location: "Austin, TX"
  },
  {
    name: "<PERSON>",
    role: "Real Estate Investor",
    content: "The ability to schedule maintenance across all properties at once saves me hours. Plus, every contractor has been professional and on time.",
    rating: 5,
    properties: "8 properties"
  },
  {
    name: "<PERSON>",
    role: "Busy Professional",
    content: "I don't have time to deal with contractors. Book online, they show up, job gets done. It's that simple. Worth every penny.",
    rating: 5,
    location: "San Francisco, CA"
  }
];

export default function Testimonials() {
  return (
    <section className="py-24 bg-gray-50/50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Section header */}
          <div className="text-center mb-16">
            <h2 className="text-gray-900 mb-4">
              Trusted by Thousands
            </h2>
            <p className="text-xl text-gray-600">
              Real stories from real customers.
            </p>
          </div>
          
          {/* Testimonials grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6 border-gray-200 hover:shadow-lg transition-shadow">
                {/* Rating */}
                <div className="flex gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-gray-900 text-gray-900" />
                  ))}
                </div>
                
                {/* Content */}
                <p className="text-gray-700 mb-6 leading-relaxed">
                  "{testimonial.content}"
                </p>
                
                {/* Author */}
                <div className="border-t border-gray-100 pt-4">
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-sm text-gray-600">{testimonial.role}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {testimonial.properties || testimonial.location}
                  </p>
                </div>
              </Card>
            ))}
          </div>
          
          {/* Bottom stat */}
          <div className="mt-16 text-center">
            <div className="inline-flex items-center gap-2 px-6 py-3 bg-white rounded-full shadow-sm">
              <div className="flex gap-0.5">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 fill-gray-900 text-gray-900" />
                ))}
              </div>
              <span className="font-semibold text-gray-900 ml-2">4.8 out of 5</span>
              <span className="text-gray-600">from 2,847 reviews</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}