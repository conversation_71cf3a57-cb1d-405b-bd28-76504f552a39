#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/is-inside-container@1.0.0/node_modules/is-inside-container/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/is-inside-container@1.0.0/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/is-inside-container@1.0.0/node_modules/is-inside-container/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/is-inside-container@1.0.0/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../is-inside-container/cli.js" "$@"
else
  exec node  "$basedir/../is-inside-container/cli.js" "$@"
fi
