@import "tailwindcss";
@import "tw-animate-css";
@import '@fontsource-variable/outfit';
@import '@fontsource-variable/dm-sans';

@custom-variant dark (&:is(.dark *));

/* Prevent transition during theme changes */
html.theme-changing * {
  transition: none !important;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --radius-2xl: calc(var(--radius) + 8px);
  
  /* Core color tokens */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  
  /* Brand colors */
  --color-forest: var(--forest);
  --color-forest-light: var(--forest-light);
  --color-terracotta: var(--terracotta);
  --color-terracotta-light: var(--terracotta-light);
  --color-teal: var(--teal);
  --color-teal-light: var(--teal-light);
  --color-sage: var(--sage);
  --color-cream: var(--cream);
  
  /* Gradient colors */
  --color-gradient-forest-from: var(--gradient-forest-from);
  --color-gradient-forest-to: var(--gradient-forest-to);
  --color-gradient-warm-from: var(--gradient-warm-from);
  --color-gradient-warm-to: var(--gradient-warm-to);
  --color-gradient-ocean-from: var(--gradient-ocean-from);
  --color-gradient-ocean-to: var(--gradient-ocean-to);
  
  /* Chart colors */
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  
  /* Sidebar colors */
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.75rem;
  
  /* Light theme - Sophisticated earth tones with vibrant accents */
  --background: oklch(0.99 0.005 60); /* Warm white with subtle warmth */
  --foreground: oklch(0.20 0.03 160); /* Deep forest gray */
  --card: oklch(1 0 0); /* Pure white */
  --card-foreground: oklch(0.20 0.03 160);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.20 0.03 160);
  
  /* Primary - Deep forest green */
  --primary: oklch(0.45 0.15 160); /* Deep forest green */
  --primary-foreground: oklch(0.99 0.005 60);
  
  /* Secondary - Warm terracotta */
  --secondary: oklch(0.65 0.18 35); /* Warm terracotta */
  --secondary-foreground: oklch(0.99 0.005 60);
  
  /* Accent - Bright teal */
  --accent: oklch(0.68 0.20 190); /* Bright teal */
  --accent-foreground: oklch(0.20 0.03 160);
  
  /* Muted tones */
  --muted: oklch(0.96 0.01 60);
  --muted-foreground: oklch(0.50 0.02 160);
  
  /* System colors */
  --destructive: oklch(0.60 0.22 25);
  --destructive-foreground: oklch(0.99 0.005 60);
  --border: oklch(0.92 0.01 60);
  --input: oklch(0.94 0.01 60);
  --ring: oklch(0.45 0.15 160);
  
  /* Brand palette */
  --forest: oklch(0.45 0.15 160);
  --forest-light: oklch(0.55 0.12 160);
  --terracotta: oklch(0.65 0.18 35);
  --terracotta-light: oklch(0.75 0.14 35);
  --teal: oklch(0.68 0.20 190);
  --teal-light: oklch(0.78 0.15 190);
  --sage: oklch(0.80 0.08 120);
  --cream: oklch(0.96 0.02 55);
  
  /* Gradients */
  --gradient-forest-from: oklch(0.45 0.15 160);
  --gradient-forest-to: oklch(0.55 0.18 180);
  --gradient-warm-from: oklch(0.65 0.18 35);
  --gradient-warm-to: oklch(0.72 0.20 50);
  --gradient-ocean-from: oklch(0.68 0.20 190);
  --gradient-ocean-to: oklch(0.65 0.22 210);
  
  /* Chart colors */
  --chart-1: oklch(0.45 0.15 160);
  --chart-2: oklch(0.65 0.18 35);
  --chart-3: oklch(0.68 0.20 190);
  --chart-4: oklch(0.80 0.08 120);
  --chart-5: oklch(0.72 0.15 280);
  
  /* Sidebar */
  --sidebar: oklch(0.99 0.005 60);
  --sidebar-foreground: oklch(0.20 0.03 160);
  --sidebar-primary: oklch(0.45 0.15 160);
  --sidebar-primary-foreground: oklch(0.99 0.005 60);
  --sidebar-accent: oklch(0.96 0.01 60);
  --sidebar-accent-foreground: oklch(0.20 0.03 160);
  --sidebar-border: oklch(0.92 0.01 60);
  --sidebar-ring: oklch(0.45 0.15 160);
}

.dark {
  /* Dark theme - Rich, sophisticated dark mode */
  --background: oklch(0.16 0.02 200); /* Deep navy blue */
  --foreground: oklch(0.96 0.01 60); /* Warm white */
  --card: oklch(0.20 0.02 200); /* Slightly lighter navy */
  --card-foreground: oklch(0.96 0.01 60);
  --popover: oklch(0.20 0.02 200);
  --popover-foreground: oklch(0.96 0.01 60);
  
  /* Primary - Bright forest green */
  --primary: oklch(0.70 0.18 160); /* Bright forest green */
  --primary-foreground: oklch(0.16 0.02 200);
  
  /* Secondary - Warm coral */
  --secondary: oklch(0.75 0.20 35); /* Bright coral */
  --secondary-foreground: oklch(0.16 0.02 200);
  
  /* Accent - Electric teal */
  --accent: oklch(0.78 0.22 190); /* Electric teal */
  --accent-foreground: oklch(0.16 0.02 200);
  
  /* Muted tones */
  --muted: oklch(0.25 0.02 200);
  --muted-foreground: oklch(0.70 0.01 60);
  
  /* System colors */
  --destructive: oklch(0.70 0.22 25);
  --destructive-foreground: oklch(0.96 0.01 60);
  --border: oklch(0.30 0.02 200);
  --input: oklch(0.25 0.02 200);
  --ring: oklch(0.70 0.18 160);
  
  /* Brand palette - Dark mode */
  --forest: oklch(0.70 0.18 160);
  --forest-light: oklch(0.80 0.15 160);
  --terracotta: oklch(0.75 0.20 35);
  --terracotta-light: oklch(0.85 0.16 35);
  --teal: oklch(0.78 0.22 190);
  --teal-light: oklch(0.88 0.18 190);
  --sage: oklch(0.85 0.10 120);
  --cream: oklch(0.96 0.01 60);
  
  /* Gradients - Dark mode */
  --gradient-forest-from: oklch(0.70 0.18 160);
  --gradient-forest-to: oklch(0.65 0.20 180);
  --gradient-warm-from: oklch(0.75 0.20 35);
  --gradient-warm-to: oklch(0.82 0.22 50);
  --gradient-ocean-from: oklch(0.78 0.22 190);
  --gradient-ocean-to: oklch(0.75 0.24 210);
  
  /* Chart colors - Dark mode */
  --chart-1: oklch(0.70 0.18 160);
  --chart-2: oklch(0.75 0.20 35);
  --chart-3: oklch(0.78 0.22 190);
  --chart-4: oklch(0.85 0.10 120);
  --chart-5: oklch(0.82 0.18 280);
  
  /* Sidebar - Dark mode */
  --sidebar: oklch(0.20 0.02 200);
  --sidebar-foreground: oklch(0.96 0.01 60);
  --sidebar-primary: oklch(0.70 0.18 160);
  --sidebar-primary-foreground: oklch(0.16 0.02 200);
  --sidebar-accent: oklch(0.25 0.02 200);
  --sidebar-accent-foreground: oklch(0.96 0.01 60);
  --sidebar-border: oklch(0.30 0.02 200);
  --sidebar-ring: oklch(0.70 0.18 160);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  
  body {
    @apply bg-background text-foreground;
    font-family: 'DM Sans Variable', 'Inter', system-ui, -apple-system, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: "ss01", "ss03";
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
    font-family: 'Outfit Variable', 'DM Sans Variable', system-ui, sans-serif;
  }
  
  h1 {
    @apply text-5xl md:text-6xl lg:text-7xl;
    line-height: 1.1;
  }
  
  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
    line-height: 1.2;
  }
  
  h3 {
    @apply text-2xl md:text-3xl;
    line-height: 1.3;
  }
  
  p {
    @apply leading-relaxed;
  }
  
  /* Custom gradients */
  .gradient-forest {
    background: linear-gradient(135deg, var(--gradient-forest-from), var(--gradient-forest-to));
  }
  
  .gradient-warm {
    background: linear-gradient(135deg, var(--gradient-warm-from), var(--gradient-warm-to));
  }
  
  .gradient-ocean {
    background: linear-gradient(135deg, var(--gradient-ocean-from), var(--gradient-ocean-to));
  }
  
  .gradient-text {
    @apply bg-clip-text text-transparent;
    background: linear-gradient(135deg, var(--gradient-forest-from), var(--gradient-ocean-to));
  }
  
  .gradient-text-warm {
    @apply bg-clip-text text-transparent;
    background: linear-gradient(135deg, var(--gradient-warm-from), var(--gradient-warm-to));
  }
  
  /* Glass morphism effects */
  .glass {
    @apply backdrop-blur-md bg-background/60 border border-border/50;
  }
  
  .glass-dark {
    @apply backdrop-blur-xl bg-background/40 border border-border/30;
  }
  
  /* Subtle animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }
  
  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  /* Hover effects */
  .hover-lift {
    @apply transition-transform duration-300 hover:-translate-y-1;
  }
  
  .hover-scale {
    @apply transition-transform duration-300 hover:scale-105;
  }
  
  /* Custom shadows */
  .shadow-forest {
    box-shadow: 0 10px 40px -10px oklch(0.45 0.15 160 / 0.3);
  }
  
  .shadow-warm {
    box-shadow: 0 10px 40px -10px oklch(0.65 0.18 35 / 0.3);
  }
  
  .shadow-ocean {
    box-shadow: 0 10px 40px -10px oklch(0.68 0.20 190 / 0.3);
  }
  
  /* Noise texture overlay */
  .noise-texture {
    position: relative;
  }
  
  .noise-texture::before {
    content: "";
    position: absolute;
    inset: 0;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.02'/%3E%3C/svg%3E");
    pointer-events: none;
    opacity: 0.5;
  }
  
  /* Grid pattern background */
  .grid-pattern {
    background-image: 
      linear-gradient(to right, var(--border) 1px, transparent 1px),
      linear-gradient(to bottom, var(--border) 1px, transparent 1px);
    background-size: 64px 64px;
  }
  
  /* Dot pattern background */
  .dot-pattern {
    background-image: radial-gradient(circle, var(--border) 1px, transparent 1px);
    background-size: 24px 24px;
  }
}

/* Custom scrollbar */
@layer utilities {
  ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted rounded-lg;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-lg;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
  
  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: oklch(var(--muted-foreground) / 0.3) var(--muted);
  }
}