import{j as e}from"./createLucideIcon.CviQQVa0.js";import{C as o}from"./card.CpJhrToJ.js";import{S as t}from"./star.CsBUrWAP.js";import"./index.RH_Wq4ov.js";import"./utils.CBfrqCZ4.js";const n=[{name:"<PERSON>",role:"Property Investor",content:"I manage 12 rental properties. Clear Quote Pro saved me 15 hours a month and $30,000 a year on repairs. The tax documentation alone is worth it.",rating:5,properties:"12 properties"},{name:"<PERSON>",role:"Homeowner",content:"Finally, no more calling around for quotes. I got my bathroom renovated 40% cheaper than my neighbor who used traditional contractors.",rating:5,location:"Seattle, WA"},{name:"<PERSON>",role:"Property Manager",content:"My tenants love how quickly repairs get done now. I love that I can manage everything from my phone. Game changer for property management.",rating:5,properties:"28 units"},{name:"<PERSON>",role:"First-time Homeowner",content:"As a new homeowner, I had no idea what repairs should cost. Clear Quote gives me confidence I'm not being taken advantage of.",rating:5,location:"Austin, TX"},{name:"<PERSON>",role:"Real Estate Investor",content:"The ability to schedule maintenance across all properties at once saves me hours. Plus, every contractor has been professional and on time.",rating:5,properties:"8 properties"},{name:"David Martinez",role:"Busy Professional",content:"I don't have time to deal with contractors. Book online, they show up, job gets done. It's that simple. Worth every penny.",rating:5,location:"San Francisco, CA"}];function p(){return e.jsx("section",{className:"py-24 bg-gray-50/50",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-6xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-gray-900 mb-4",children:"Trusted by Thousands"}),e.jsx("p",{className:"text-xl text-gray-600",children:"Real stories from real customers."})]}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:n.map((a,r)=>e.jsxs(o,{className:"p-6 border-gray-200 hover:shadow-lg transition-shadow",children:[e.jsx("div",{className:"flex gap-1 mb-4",children:[...Array(a.rating)].map((i,s)=>e.jsx(t,{className:"w-4 h-4 fill-gray-900 text-gray-900"},s))}),e.jsxs("p",{className:"text-gray-700 mb-6 leading-relaxed",children:['"',a.content,'"']}),e.jsxs("div",{className:"border-t border-gray-100 pt-4",children:[e.jsx("p",{className:"font-semibold text-gray-900",children:a.name}),e.jsx("p",{className:"text-sm text-gray-600",children:a.role}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:a.properties||a.location})]})]},r))}),e.jsx("div",{className:"mt-16 text-center",children:e.jsxs("div",{className:"inline-flex items-center gap-2 px-6 py-3 bg-white rounded-full shadow-sm",children:[e.jsx("div",{className:"flex gap-0.5",children:[...Array(5)].map((a,r)=>e.jsx(t,{className:"w-5 h-5 fill-gray-900 text-gray-900"},r))}),e.jsx("span",{className:"font-semibold text-gray-900 ml-2",children:"4.8 out of 5"}),e.jsx("span",{className:"text-gray-600",children:"from 2,847 reviews"})]})})]})})})}export{p as default};
