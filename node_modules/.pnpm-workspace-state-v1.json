{"lastValidatedTimestamp": 1754588736383, "projects": {"/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro": {"name": "clear-quote-pro"}, "/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/apps/landing": {"name": "landing", "version": "0.0.1"}, "/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/packages/eslint-config": {"name": "@repo/eslint-config", "version": "0.0.0"}, "/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/packages/typescript-config": {"name": "@repo/typescript-config", "version": "0.0.0"}, "/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/packages/ui": {"name": "@repo/ui", "version": "0.0.0"}}, "pnpmfiles": [], "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["apps/*", "packages/*"]}, "filteredInstall": true}