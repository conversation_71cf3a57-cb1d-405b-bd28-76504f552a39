# Clear Quote Pro

A transparent marketplace connecting homeowners and property managers with pre-vetted contractors at pre-negotiated prices. Skip the quotes, get the work done.

## Overview

Clear Quote Pro eliminates the hassle of getting multiple quotes for home repairs and maintenance. We provide instant, transparent pricing from licensed, insured, and background-checked contractors.

### Key Features

- **Instant Pricing**: See exact costs upfront for 20+ home services
- **Vetted Professionals**: All contractors are licensed, insured, and background-checked
- **Protected Payments**: Milestone-based payments with 60-day quality guarantee
- **Portfolio Management**: Manage repairs across multiple properties from one dashboard
- **Tax-Ready Documentation**: Automated invoicing and expense tracking

## Tech Stack

- **Framework**: [Astro 5](https://astro.build) with React 19 islands
- **Styling**: [Tailwind CSS v4](https://tailwindcss.com) with [shadcn/ui](https://ui.shadcn.com)
- **Database**: PostgreSQL with [Drizzle ORM](https://orm.drizzle.team)
- **Authentication**: [Better Auth](https://www.better-auth.com)
- **Monorepo**: [Turborepo](https://turborepo.com) with pnpm workspaces
- **Deployment**: Vercel (frontend) + Azure (database via Neon)

## Project Structure

```
clear-quote-pro/
├── apps/
│   ├── landing/          # Astro landing page (live)
│   ├── homeowner-app/    # Homeowner dashboard (planned)
│   ├── vendor-dashboard/ # Contractor portal (planned)
│   └── admin-dashboard/  # Admin panel (planned)
├── packages/
│   ├── ui/              # Shared shadcn components
│   ├── database/        # Shared database schemas
│   ├── auth/           # Shared auth configuration
│   └── config/         # Shared configurations
└── plan/               # Project planning documents
```

## Getting Started

### Prerequisites

- Node.js 18+
- pnpm 8+
- PostgreSQL (local development)

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/clear-quote-pro.git
cd clear-quote-pro

# Install dependencies
pnpm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your database credentials

# Run database migrations
pnpm db:push

# Start development servers
pnpm dev
```

### Development

```bash
# Start all apps in development mode
pnpm dev

# Start specific app
pnpm --filter landing dev

# Build all apps
pnpm build

# Run type checking
pnpm check-types

# Run linting
pnpm lint
```

## Available Scripts

| Command | Description |
|---------|-------------|
| `pnpm dev` | Start all apps in development mode |
| `pnpm build` | Build all apps for production |
| `pnpm lint` | Run ESLint across all packages |
| `pnpm check-types` | Run TypeScript type checking |
| `pnpm db:push` | Push database schema changes |
| `pnpm db:studio` | Open Drizzle Studio for database management |

## Apps

### Landing Page (`apps/landing`)

The marketing website built with Astro 5 and React islands for interactivity.

- **URL**: http://localhost:4321
- **Stack**: Astro, React 19, Tailwind CSS v4
- **Features**: 
  - Server-side rendering for SEO
  - React islands for interactive components
  - Responsive design with shadcn/ui components

### Homeowner App (Coming Soon)

Dashboard for homeowners to manage repairs and maintenance.

### Vendor Dashboard (Coming Soon)

Portal for contractors to manage jobs and availability.

### Admin Dashboard (Coming Soon)

Internal tools for platform management.

## Packages

### UI Components (`packages/ui`)

Shared component library using shadcn/ui with Tailwind CSS v4.

```tsx
import { Button } from '@repo/ui/button'
import { Card } from '@repo/ui/card'
```

### Database (`packages/database`)

Shared database schemas and migrations using Drizzle ORM.

### Auth (`packages/auth`)

Shared authentication configuration using Better Auth.

## Deployment

### Production

The landing page is deployed to Vercel with automatic deployments from the `main` branch.

```bash
# Build for production
pnpm build

# Preview production build locally
pnpm preview
```

### Environment Variables

```bash
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/clearquotepro"

# Production Database (Neon on Azure)
DATABASE_URL_PROD="postgresql://user:<EMAIL>/clearquotepro"

# Authentication
AUTH_SECRET="your-auth-secret"

# Email Service
SENDGRID_API_KEY="your-sendgrid-key"
EMAIL_FROM="<EMAIL>"

# Analytics
PUBLIC_SIMPLE_ANALYTICS_ID="your-id"
```

## Contributing

1. Create a feature branch from `main`
2. Make your changes
3. Run tests and type checking
4. Submit a pull request

## Architecture Decisions

- **Monorepo**: Enables code sharing and consistent deployments
- **Astro with React Islands**: SEO-friendly with selective interactivity
- **Tailwind CSS v4**: Modern styling with design tokens
- **PostgreSQL + Drizzle**: Type-safe database queries
- **Better Auth**: Modern authentication with excellent DX

## Roadmap

- [x] Landing page with service listings
- [x] Responsive design with shadcn components
- [ ] Early access signup flow
- [ ] Homeowner dashboard MVP
- [ ] Contractor onboarding portal
- [ ] Payment integration (Stripe)
- [ ] Job scheduling system
- [ ] Review and rating system
- [ ] Admin dashboard
- [ ] Mobile apps (React Native)

## License

Proprietary - All rights reserved

## Support

For questions or support, contact: <EMAIL>

---

Built with care by Ray Labs Studio