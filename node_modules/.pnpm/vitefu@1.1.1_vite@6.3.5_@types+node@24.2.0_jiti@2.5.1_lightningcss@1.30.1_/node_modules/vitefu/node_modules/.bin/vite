#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/vite@6.3.5_@types+node@24.2.0_jiti@2.5.1_lightningcss@1.30.1/node_modules/vite/bin/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/vite@6.3.5_@types+node@24.2.0_jiti@2.5.1_lightningcss@1.30.1/node_modules/vite/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/vite@6.3.5_@types+node@24.2.0_jiti@2.5.1_lightningcss@1.30.1/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/vite@6.3.5_@types+node@24.2.0_jiti@2.5.1_lightningcss@1.30.1/node_modules/vite/bin/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/vite@6.3.5_@types+node@24.2.0_jiti@2.5.1_lightningcss@1.30.1/node_modules/vite/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/vite@6.3.5_@types+node@24.2.0_jiti@2.5.1_lightningcss@1.30.1/node_modules:/Users/<USER>/Documents/Development/RayLabsStudio/clear-quote-pro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../vite@6.3.5_@types+node@24.2.0_jiti@2.5.1_lightningcss@1.30.1/node_modules/vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../../../vite@6.3.5_@types+node@24.2.0_jiti@2.5.1_lightningcss@1.30.1/node_modules/vite/bin/vite.js" "$@"
fi
