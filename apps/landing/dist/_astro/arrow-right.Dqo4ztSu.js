import{r as s}from"./index.RH_Wq4ov.js";import{j as p,c as g}from"./createLucideIcon.CviQQVa0.js";import{a as x}from"./utils.CBfrqCZ4.js";function h(n,e){if(typeof n=="function")return n(e);n!=null&&(n.current=e)}function N(...n){return e=>{let t=!1;const o=n.map(r=>{const l=h(r,e);return!t&&typeof l=="function"&&(t=!0),l});if(t)return()=>{for(let r=0;r<o.length;r++){const l=o[r];typeof l=="function"?l():h(n[r],null)}}}}function R(n){const e=j(n),t=s.forwardRef((o,r)=>{const{children:l,...c}=o,a=s.Children.toArray(l),f=a.find(O);if(f){const i=f.props.children,u=a.map(d=>d===f?s.Children.count(i)>1?s.Children.only(null):s.isValidElement(i)?i.props.children:null:d);return p.jsx(e,{...c,ref:r,children:s.isValidElement(i)?s.cloneElement(i,void 0,u):null})}return p.jsx(e,{...c,ref:r,children:l})});return t.displayName=`${n}.Slot`,t}var w=R("Slot");function j(n){const e=s.forwardRef((t,o)=>{const{children:r,...l}=t;if(s.isValidElement(r)){const c=A(r),a=_(l,r.props);return r.type!==s.Fragment&&(a.ref=o?N(o,c):c),s.cloneElement(r,a)}return s.Children.count(r)>1?s.Children.only(null):null});return e.displayName=`${n}.SlotClone`,e}var b=Symbol("radix.slottable");function O(n){return s.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===b}function _(n,e){const t={...e};for(const o in e){const r=n[o],l=e[o];/^on[A-Z]/.test(o)?r&&l?t[o]=(...a)=>{const f=l(...a);return r(...a),f}:r&&(t[o]=r):o==="style"?t[o]={...r,...l}:o==="className"&&(t[o]=[r,l].filter(Boolean).join(" "))}return{...n,...t}}function A(n){let e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get,t=e&&"isReactWarning"in e&&e.isReactWarning;return t?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get,t=e&&"isReactWarning"in e&&e.isReactWarning,t?n.props.ref:n.props.ref||n.ref)}const C=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,S=x,T=(n,e)=>t=>{var o;if(e?.variants==null)return S(n,t?.class,t?.className);const{variants:r,defaultVariants:l}=e,c=Object.keys(r).map(i=>{const u=t?.[i],d=l?.[i];if(u===null)return null;const m=C(u)||C(d);return r[i][m]}),a=t&&Object.entries(t).reduce((i,u)=>{let[d,m]=u;return m===void 0||(i[d]=m),i},{}),f=e==null||(o=e.compoundVariants)===null||o===void 0?void 0:o.reduce((i,u)=>{let{class:d,className:m,...E}=u;return Object.entries(E).every(V=>{let[v,y]=V;return Array.isArray(y)?y.includes({...l,...a}[v]):{...l,...a}[v]===y})?[...i,d,m]:i},[]);return S(n,c,f,t?.class,t?.className)};/**
 * @license lucide-react v0.537.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],D=g("arrow-right",P);export{D as A,w as S,T as c};
