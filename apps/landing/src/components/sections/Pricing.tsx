import { Check } from 'lucide-react';
import { Card } from '@repo/ui/card';

export default function Pricing() {
  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto">
          {/* Section header */}
          <div className="text-center mb-16">
            <h2 className="text-gray-900 mb-4">
              No Hidden Fees. Ever.
            </h2>
            <p className="text-xl text-gray-600">
              See exactly what you'll pay before you book.
            </p>
          </div>
          
          {/* Pricing example */}
          <Card className="max-w-2xl mx-auto p-8 border-gray-200">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Example: Interior Painting Project
              </h3>
              <p className="text-gray-600">
                1,000 square feet, 2 coats, quality paint
              </p>
            </div>
            
            <div className="space-y-3 pb-6 border-b border-gray-100">
              <PriceRow label="Labor" value="$850" />
              <PriceRow label="Materials (Premium Paint)" value="$400" />
              <PriceRow label="Platform Fee (20%)" value="$250" />
            </div>
            
            <div className="flex justify-between items-center pt-6 pb-4">
              <span className="text-lg font-semibold text-gray-900">Your Total</span>
              <span className="text-3xl font-bold text-gray-900">$1,500</span>
            </div>
            
            <div className="bg-gray-50 -mx-8 -mb-8 mt-6 p-6 rounded-b-lg">
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">Traditional Quotes Range</span>
                <span className="text-gray-500 line-through">$1,800-2,400</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-semibold text-gray-900">Your Savings</span>
                <span className="font-semibold text-green-600">$300-900</span>
              </div>
            </div>
          </Card>
          
          {/* What's included */}
          <div className="mt-16 grid md:grid-cols-2 gap-12 max-w-4xl mx-auto">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Always Included
              </h3>
              <ul className="space-y-3">
                {[
                  "All labor costs",
                  "Standard materials",
                  "Insurance coverage",
                  "Quality guarantee",
                  "Photo documentation",
                  "Tax-ready invoicing"
                ].map((item, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <Check className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Never Charged For
              </h3>
              <ul className="space-y-3">
                {[
                  "Quotes or estimates",
                  "Scheduling changes",
                  "Payment processing",
                  "Contractor matching",
                  "Basic documentation",
                  "Customer support"
                ].map((item, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <Check className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function PriceRow({ label, value }: { label: string; value: string }) {
  return (
    <div className="flex justify-between items-center">
      <span className="text-gray-600">{label}</span>
      <span className="font-medium text-gray-900">{value}</span>
    </div>
  );
}