# Database Development - Clear Quote Pro

## Implementation with Drizzle ORM

This document provides the complete implementation guide for the Clear Quote Pro database using Drizzle ORM with PostgreSQL.

## Project Structure

```
packages/database/
├── src/
│   ├── schema/
│   │   ├── index.ts           # Re-exports all schemas
│   │   ├── auth.ts            # User authentication tables
│   │   ├── users.ts           # User profiles
│   │   ├── properties.ts      # Property management
│   │   ├── vendors.ts         # Vendor management
│   │   ├── services.ts        # Service configuration
│   │   ├── projects.ts        # Project management
│   │   ├── payments.ts        # Financial transactions
│   │   ├── communications.ts  # Messages & notifications
│   │   ├── reviews.ts         # Reviews & ratings
│   │   ├── disputes.ts        # Dispute management
│   │   ├── admin.ts           # Admin & platform
│   │   └── analytics.ts       # Analytics tables
│   ├── migrations/
│   │   └── [timestamp]_initial.sql
│   ├── seed/
│   │   ├── index.ts           # Main seed script
│   │   ├── services.ts        # Service data
│   │   ├── pricing.ts         # Pricing rules
│   │   └── test-data.ts       # Development data
│   ├── queries/
│   │   ├── users.ts           # User queries
│   │   ├── vendors.ts         # Vendor queries
│   │   ├── projects.ts        # Project queries
│   │   └── analytics.ts       # Analytics queries
│   ├── client.ts              # Database client setup
│   ├── index.ts               # Main export
│   └── types.ts               # TypeScript types
├── drizzle.config.ts          # Drizzle configuration
├── package.json
└── tsconfig.json
```

## Installation & Setup

### Package Dependencies

```json
{
  "name": "@clear-quote-pro/database",
  "version": "1.0.0",
  "main": "./src/index.ts",
  "types": "./src/index.ts",
  "scripts": {
    "db:generate": "drizzle-kit generate:pg",
    "db:migrate": "tsx src/migrate.ts",
    "db:push": "drizzle-kit push:pg",
    "db:seed": "tsx src/seed/index.ts",
    "db:studio": "drizzle-kit studio",
    "db:drop": "drizzle-kit drop",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "drizzle-orm": "^0.29.0",
    "postgres": "^3.4.0",
    "@neondatabase/serverless": "^0.6.0",
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "drizzle-kit": "^0.20.0",
    "@types/node": "^20.0.0",
    "tsx": "^4.0.0",
    "typescript": "^5.3.0"
  }
}
```

## Database Configuration

### Drizzle Config

```typescript
// packages/database/drizzle.config.ts

import type { Config } from 'drizzle-kit'
import * as dotenv from 'dotenv'

dotenv.config({ path: '../../.env.local' })

export default {
  schema: './src/schema/*.ts',
  out: './src/migrations',
  driver: 'pg',
  dbCredentials: {
    connectionString: process.env.DATABASE_URL!
  },
  verbose: true,
  strict: true
} satisfies Config
```

### Database Client

```typescript
// packages/database/src/client.ts

import { drizzle } from 'drizzle-orm/postgres-js'
import { drizzle as drizzleNeon } from 'drizzle-orm/neon-http'
import { neon, neonConfig } from '@neondatabase/serverless'
import postgres from 'postgres'
import * as schema from './schema'

// Development: Local PostgreSQL
function createDevClient() {
  const connectionString = process.env.DATABASE_URL!
  const client = postgres(connectionString)
  return drizzle(client, { schema })
}

// Production: Neon PostgreSQL
function createProdClient() {
  neonConfig.fetchConnectionCache = true
  
  const sql = neon(process.env.DATABASE_URL!)
  return drizzleNeon(sql, { schema })
}

// Export appropriate client based on environment
export const db = process.env.NODE_ENV === 'production' 
  ? createProdClient() 
  : createDevClient()

export type Database = typeof db
```

## Schema Implementation

### User Authentication Schema

```typescript
// packages/database/src/schema/auth.ts

import { 
  pgTable, 
  uuid, 
  varchar, 
  boolean, 
  timestamp,
  text
} from 'drizzle-orm/pg-core'

export const users = pgTable('users', {
  id: uuid('id').defaultRandom().primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  emailVerified: boolean('email_verified').default(false),
  passwordHash: text('password_hash'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
})

export const sessions = pgTable('sessions', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  token: varchar('token', { length: 255 }).notNull().unique(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull()
})

export const verificationTokens = pgTable('verification_tokens', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  token: varchar('token', { length: 255 }).notNull().unique(),
  type: varchar('type', { length: 50 }).notNull(), // 'email', 'phone', 'password_reset'
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull()
})
```

### User Profiles Schema

```typescript
// packages/database/src/schema/users.ts

import { 
  pgTable, 
  uuid, 
  varchar, 
  boolean, 
  timestamp,
  text,
  pgEnum,
  jsonb
} from 'drizzle-orm/pg-core'
import { users } from './auth'

export const userTypeEnum = pgEnum('user_type', [
  'homeowner', 
  'landlord', 
  'vendor', 
  'admin'
])

export const userProfiles = pgTable('user_profiles', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => users.id).notNull().unique(),
  userType: userTypeEnum('user_type').notNull(),
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  phone: varchar('phone', { length: 20 }),
  phoneVerified: boolean('phone_verified').default(false),
  avatarUrl: text('avatar_url'),
  address: text('address'),
  city: varchar('city', { length: 100 }),
  state: varchar('state', { length: 2 }),
  zip: varchar('zip', { length: 10 }),
  profileComplete: boolean('profile_complete').default(false),
  stripeCustomerId: varchar('stripe_customer_id', { length: 100 }),
  notificationPreferences: jsonb('notification_preferences').$type<{
    email: boolean,
    sms: boolean,
    push: boolean,
    marketing: boolean
  }>(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
})

// Relations
export const userProfileRelations = relations(userProfiles, ({ one }) => ({
  user: one(users, {
    fields: [userProfiles.userId],
    references: [users.id]
  })
}))
```

### Properties Schema

```typescript
// packages/database/src/schema/properties.ts

import { 
  pgTable, 
  uuid, 
  varchar, 
  boolean, 
  timestamp,
  text,
  integer,
  decimal,
  pgEnum,
  jsonb
} from 'drizzle-orm/pg-core'
import { users } from './auth'

export const propertyTypeEnum = pgEnum('property_type', [
  'single_family',
  'condo',
  'townhouse',
  'multi_family'
])

export const properties = pgTable('properties', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  nickname: varchar('nickname', { length: 100 }),
  address: text('address').notNull(),
  city: varchar('city', { length: 100 }).notNull(),
  state: varchar('state', { length: 2 }).notNull(),
  zip: varchar('zip', { length: 10 }).notNull(),
  propertyType: propertyTypeEnum('property_type').notNull(),
  yearBuilt: integer('year_built'),
  sqft: integer('sqft'),
  bedrooms: integer('bedrooms'),
  bathrooms: decimal('bathrooms', { precision: 3, scale: 1 }),
  isPrimary: boolean('is_primary').default(false),
  isRental: boolean('is_rental').default(false),
  tenantInfo: jsonb('tenant_info').$type<{
    name?: string,
    phone?: string,
    email?: string
  }>(),
  accessInstructions: text('access_instructions'),
  photos: jsonb('photos').$type<string[]>(),
  sibiPropertyId: varchar('sibi_property_id', { length: 100 }),
  metadata: jsonb('metadata'),
  deletedAt: timestamp('deleted_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
})

// Indexes
export const propertiesIndexes = {
  userIdIdx: index('properties_user_id_idx').on(properties.userId),
  zipIdx: index('properties_zip_idx').on(properties.zip)
}
```

### Vendors Schema

```typescript
// packages/database/src/schema/vendors.ts

import { 
  pgTable, 
  uuid, 
  varchar, 
  boolean, 
  timestamp,
  text,
  integer,
  decimal,
  pgEnum,
  jsonb,
  index,
  uniqueIndex
} from 'drizzle-orm/pg-core'
import { users } from './auth'
import { relations } from 'drizzle-orm'

export const vendorStatusEnum = pgEnum('vendor_status', [
  'pending',
  'active',
  'suspended',
  'inactive'
])

export const businessTypeEnum = pgEnum('business_type', [
  'sole_prop',
  'llc',
  'corp',
  's_corp'
])

export const vendors = pgTable('vendors', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => users.id).notNull().unique(),
  businessName: varchar('business_name', { length: 255 }).notNull(),
  dba: varchar('dba', { length: 255 }),
  businessType: businessTypeEnum('business_type'),
  tinEncrypted: text('tin_encrypted'), // Encrypted TIN
  businessAddress: text('business_address').notNull(),
  businessCity: varchar('business_city', { length: 100 }).notNull(),
  businessState: varchar('business_state', { length: 2 }).notNull(),
  businessZip: varchar('business_zip', { length: 10 }).notNull(),
  businessPhone: varchar('business_phone', { length: 20 }).notNull(),
  website: text('website'),
  yearsInBusiness: integer('years_in_business'),
  employeeCount: integer('employee_count'),
  status: vendorStatusEnum('status').notNull().default('pending'),
  verifiedAt: timestamp('verified_at'),
  verifiedBy: uuid('verified_by'), // References admin_users
  suspendedAt: timestamp('suspended_at'),
  suspensionReason: text('suspension_reason'),
  serviceRadius: integer('service_radius').default(25),
  totalJobsCompleted: integer('total_jobs_completed').default(0),
  totalEarnings: decimal('total_earnings', { precision: 12, scale: 2 }).default('0'),
  averageRating: decimal('average_rating', { precision: 3, scale: 2 }),
  responseRate: decimal('response_rate', { precision: 5, scale: 2 }),
  onTimeRate: decimal('on_time_rate', { precision: 5, scale: 2 }),
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
}, (table) => {
  return {
    statusIdx: index('vendors_status_idx').on(table.status),
    userIdIdx: uniqueIndex('vendors_user_id_idx').on(table.userId)
  }
})

export const vendorServices = pgTable('vendor_services', {
  id: uuid('id').defaultRandom().primaryKey(),
  vendorId: uuid('vendor_id').references(() => vendors.id).notNull(),
  serviceId: uuid('service_id').notNull(), // References services table
  subServices: jsonb('sub_services').$type<string[]>(),
  experienceYears: integer('experience_years'),
  requiresLicense: boolean('requires_license').default(false),
  licenseNumber: varchar('license_number', { length: 100 }),
  licenseExpiry: timestamp('license_expiry'),
  licenseVerified: boolean('license_verified').default(false),
  customPricing: jsonb('custom_pricing'),
  active: boolean('active').default(true),
  createdAt: timestamp('created_at').defaultNow().notNull()
}, (table) => {
  return {
    vendorIdIdx: index('vendor_services_vendor_id_idx').on(table.vendorId)
  }
})

export const documentTypeEnum = pgEnum('document_type', [
  'insurance_coi',
  'business_license',
  'trade_license',
  'w9_tax',
  'background_check',
  'other'
])

export const documentStatusEnum = pgEnum('document_status', [
  'pending',
  'verified',
  'expired',
  'rejected'
])

export const vendorDocuments = pgTable('vendor_documents', {
  id: uuid('id').defaultRandom().primaryKey(),
  vendorId: uuid('vendor_id').references(() => vendors.id).notNull(),
  documentType: documentTypeEnum('document_type').notNull(),
  documentName: varchar('document_name', { length: 255 }).notNull(),
  fileUrl: text('file_url').notNull(),
  fileSize: integer('file_size'),
  issueDate: timestamp('issue_date'),
  expiryDate: timestamp('expiry_date'),
  documentNumber: varchar('document_number', { length: 100 }),
  status: documentStatusEnum('status').notNull().default('pending'),
  verifiedBy: uuid('verified_by'), // References admin_users
  verifiedAt: timestamp('verified_at'),
  rejectionReason: text('rejection_reason'),
  reminderSent30Days: boolean('reminder_sent_30_days').default(false),
  reminderSent60Days: boolean('reminder_sent_60_days').default(false),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
}, (table) => {
  return {
    vendorStatusIdx: index('vendor_documents_vendor_status_idx')
      .on(table.vendorId, table.status)
  }
})

export const vendorServiceAreas = pgTable('vendor_service_areas', {
  id: uuid('id').defaultRandom().primaryKey(),
  vendorId: uuid('vendor_id').references(() => vendors.id).notNull(),
  coverageType: varchar('coverage_type', { length: 20 }).notNull(), // 'metro', 'county', 'city', 'zip'
  metroAreas: jsonb('metro_areas').$type<string[]>(),
  counties: jsonb('counties').$type<string[]>(),
  cities: jsonb('cities').$type<string[]>(),
  zipCodes: jsonb('zip_codes').$type<string[]>(),
  maxDistance: integer('max_distance'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
})

// Vendor relations
export const vendorRelations = relations(vendors, ({ many, one }) => ({
  services: many(vendorServices),
  documents: many(vendorDocuments),
  serviceAreas: many(vendorServiceAreas),
  user: one(users, {
    fields: [vendors.userId],
    references: [users.id]
  })
}))
```

### Services Schema

```typescript
// packages/database/src/schema/services.ts

import { 
  pgTable, 
  uuid, 
  varchar, 
  boolean, 
  timestamp,
  text,
  decimal,
  jsonb,
  uniqueIndex
} from 'drizzle-orm/pg-core'

export const services = pgTable('services', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  slug: varchar('slug', { length: 100 }).notNull().unique(),
  category: varchar('category', { length: 50 }).notNull(),
  description: text('description'),
  basePrice: decimal('base_price', { precision: 10, scale: 2 }),
  priceUnit: varchar('price_unit', { length: 20 }), // 'sqft', 'hour', 'project'
  requiresLicense: boolean('requires_license').default(false),
  icon: varchar('icon', { length: 50 }),
  active: boolean('active').default(true),
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
}, (table) => {
  return {
    slugIdx: uniqueIndex('services_slug_idx').on(table.slug)
  }
})

export const servicePricingRules = pgTable('service_pricing_rules', {
  id: uuid('id').defaultRandom().primaryKey(),
  serviceId: uuid('service_id').references(() => services.id).notNull(),
  name: varchar('name', { length: 100 }).notNull(),
  conditionType: varchar('condition_type', { length: 50 }), // 'variant', 'region', 'season'
  conditions: jsonb('conditions').notNull(),
  priceAdjustment: decimal('price_adjustment', { precision: 10, scale: 2 }),
  priceMultiplier: decimal('price_multiplier', { precision: 5, scale: 3 }),
  priority: integer('priority').default(0),
  active: boolean('active').default(true),
  createdAt: timestamp('created_at').defaultNow().notNull()
})
```

### Projects Schema

```typescript
// packages/database/src/schema/projects.ts

import { 
  pgTable, 
  uuid, 
  varchar, 
  boolean, 
  timestamp,
  text,
  decimal,
  integer,
  pgEnum,
  jsonb,
  index,
  date
} from 'drizzle-orm/pg-core'
import { users } from './auth'
import { properties } from './properties'
import { services } from './services'
import { vendors } from './vendors'

export const projectStatusEnum = pgEnum('project_status', [
  'draft',
  'pending_vendors',
  'vendor_selected',
  'scheduled',
  'in_progress',
  'completed',
  'cancelled'
])

export const projects = pgTable('projects', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  propertyId: uuid('property_id').references(() => properties.id),
  serviceId: uuid('service_id').references(() => services.id),
  vendorId: uuid('vendor_id').references(() => vendors.id),
  status: projectStatusEnum('status').notNull().default('draft'),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description'),
  scope: jsonb('scope').notNull().$type<{
    rooms?: Array<{
      name: string,
      length: number,
      width: number,
      height: number
    }>,
    options?: Record<string, any>
  }>(),
  measurements: jsonb('measurements'),
  materials: jsonb('materials'),
  photosBefore: jsonb('photos_before').$type<string[]>(),
  photosDuring: jsonb('photos_during').$type<string[]>(),
  photosAfter: jsonb('photos_after').$type<string[]>(),
  laborCost: decimal('labor_cost', { precision: 10, scale: 2 }),
  materialCost: decimal('material_cost', { precision: 10, scale: 2 }),
  platformFee: decimal('platform_fee', { precision: 10, scale: 2 }),
  totalCost: decimal('total_cost', { precision: 10, scale: 2 }).notNull(),
  preferredStartDate: date('preferred_start_date'),
  scheduledStartDate: date('scheduled_start_date'),
  scheduledEndDate: date('scheduled_end_date'),
  actualStartDate: date('actual_start_date'),
  actualEndDate: date('actual_end_date'),
  cancellationReason: text('cancellation_reason'),
  cancelledBy: uuid('cancelled_by').references(() => users.id),
  cancelledAt: timestamp('cancelled_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
}, (table) => {
  return {
    userStatusIdx: index('projects_user_status_idx').on(table.userId, table.status),
    vendorStatusIdx: index('projects_vendor_status_idx').on(table.vendorId, table.status),
    propertyIdx: index('projects_property_idx').on(table.propertyId)
  }
})

export const projectVendorResponseEnum = pgEnum('project_vendor_response', [
  'pending',
  'accepted',
  'declined',
  'selected'
])

export const projectVendors = pgTable('project_vendors', {
  id: uuid('id').defaultRandom().primaryKey(),
  projectId: uuid('project_id').references(() => projects.id).notNull(),
  vendorId: uuid('vendor_id').references(() => vendors.id).notNull(),
  status: projectVendorResponseEnum('status').notNull().default('pending'),
  availableSlots: jsonb('available_slots').$type<Array<{
    start: string,
    end: string
  }>>(),
  proposedStartDate: date('proposed_start_date'),
  estimatedDuration: integer('estimated_duration'), // hours
  crewSize: integer('crew_size'),
  message: text('message'),
  respondedAt: timestamp('responded_at'),
  selectedAt: timestamp('selected_at'),
  createdAt: timestamp('created_at').defaultNow().notNull()
}, (table) => {
  return {
    projectIdx: index('project_vendors_project_idx').on(table.projectId)
  }
})
```

### Payments Schema

```typescript
// packages/database/src/schema/payments.ts

import { 
  pgTable, 
  uuid, 
  varchar, 
  timestamp,
  decimal,
  pgEnum,
  jsonb,
  index
} from 'drizzle-orm/pg-core'
import { projects } from './projects'
import { users } from './auth'
import { vendors } from './vendors'

export const paymentTypeEnum = pgEnum('payment_type', [
  'deposit',
  'progress',
  'final',
  'refund'
])

export const paymentStatusEnum = pgEnum('payment_status', [
  'pending',
  'processing',
  'completed',
  'failed',
  'refunded'
])

export const payments = pgTable('payments', {
  id: uuid('id').defaultRandom().primaryKey(),
  projectId: uuid('project_id').references(() => projects.id).notNull(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  vendorId: uuid('vendor_id').references(() => vendors.id),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  type: paymentTypeEnum('type').notNull(),
  milestone: varchar('milestone', { length: 50 }),
  status: paymentStatusEnum('status').notNull().default('pending'),
  paymentMethod: varchar('payment_method', { length: 50 }), // 'card', 'ach', 'affirm'
  paymentIntentId: varchar('payment_intent_id', { length: 100 }),
  transactionId: varchar('transaction_id', { length: 100 }),
  receiptUrl: text('receipt_url'),
  failureReason: text('failure_reason'),
  refundAmount: decimal('refund_amount', { precision: 10, scale: 2 }),
  refundedAt: timestamp('refunded_at'),
  paidAt: timestamp('paid_at'),
  createdAt: timestamp('created_at').defaultNow().notNull()
}, (table) => {
  return {
    projectIdx: index('payments_project_idx').on(table.projectId),
    userIdx: index('payments_user_idx').on(table.userId)
  }
})

export const payoutMethodEnum = pgEnum('payout_method', [
  'ach',
  'wire',
  'check'
])

export const payoutStatusEnum = pgEnum('payout_status', [
  'pending',
  'processing',
  'completed',
  'failed'
])

export const vendorPayouts = pgTable('vendor_payouts', {
  id: uuid('id').defaultRandom().primaryKey(),
  vendorId: uuid('vendor_id').references(() => vendors.id).notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  method: payoutMethodEnum('method').notNull(),
  status: payoutStatusEnum('status').notNull().default('pending'),
  transactionId: varchar('transaction_id', { length: 100 }),
  referenceNumber: varchar('reference_number', { length: 100 }),
  projectIds: jsonb('project_ids').$type<string[]>(),
  initiatedBy: uuid('initiated_by'), // References admin_users
  initiatedAt: timestamp('initiated_at').defaultNow(),
  processedAt: timestamp('processed_at'),
  completedAt: timestamp('completed_at'),
  failureReason: text('failure_reason'),
  retryCount: integer('retry_count').default(0)
}, (table) => {
  return {
    vendorStatusIdx: index('vendor_payouts_vendor_status_idx')
      .on(table.vendorId, table.status)
  }
})
```

### Complete Index Export

```typescript
// packages/database/src/schema/index.ts

// Auth
export * from './auth'

// Users
export * from './users'

// Properties
export * from './properties'

// Vendors
export * from './vendors'

// Services
export * from './services'

// Projects
export * from './projects'

// Payments
export * from './payments'

// Communications
export * from './communications'

// Reviews
export * from './reviews'

// Disputes
export * from './disputes'

// Admin
export * from './admin'

// Analytics
export * from './analytics'
```

## Migration Script

```typescript
// packages/database/src/migrate.ts

import { migrate } from 'drizzle-orm/postgres-js/migrator'
import { db } from './client'

async function runMigrations() {
  console.log('Running migrations...')
  
  try {
    await migrate(db, { migrationsFolder: './src/migrations' })
    console.log('Migrations completed successfully')
    process.exit(0)
  } catch (error) {
    console.error('Migration failed:', error)
    process.exit(1)
  }
}

runMigrations()
```

## Seed Data

```typescript
// packages/database/src/seed/index.ts

import { db } from '../client'
import { services, servicePricingRules } from '../schema/services'
import { seedServices } from './services'
import { seedPricingRules } from './pricing'
import { seedTestData } from './test-data'

async function seed() {
  console.log('Seeding database...')
  
  try {
    // Clear existing data
    await db.delete(servicePricingRules)
    await db.delete(services)
    
    // Seed services
    const serviceIds = await seedServices(db)
    
    // Seed pricing rules
    await seedPricingRules(db, serviceIds)
    
    // Seed test data (development only)
    if (process.env.NODE_ENV === 'development') {
      await seedTestData(db)
    }
    
    console.log('Seeding completed successfully')
    process.exit(0)
  } catch (error) {
    console.error('Seeding failed:', error)
    process.exit(1)
  }
}

seed()
```

### Service Seed Data

```typescript
// packages/database/src/seed/services.ts

export async function seedServices(db: Database) {
  const servicesData = [
    {
      name: 'Interior Painting',
      slug: 'interior-painting',
      category: 'Painting',
      description: 'Professional interior painting services',
      basePrice: '1.50',
      priceUnit: 'sqft',
      requiresLicense: false,
      icon: '🎨',
      active: true
    },
    {
      name: 'Plumbing Repairs',
      slug: 'plumbing-repairs',
      category: 'Plumbing',
      description: 'Licensed plumbing repair services',
      basePrice: '125.00',
      priceUnit: 'hour',
      requiresLicense: true,
      icon: '🔧',
      active: true
    },
    {
      name: 'Electrical Work',
      slug: 'electrical-work',
      category: 'Electrical',
      description: 'Licensed electrical services',
      basePrice: '150.00',
      priceUnit: 'hour',
      requiresLicense: true,
      icon: '⚡',
      active: true
    },
    {
      name: 'HVAC Service',
      slug: 'hvac-service',
      category: 'HVAC',
      description: 'Heating and cooling system service',
      basePrice: '189.00',
      priceUnit: 'service',
      requiresLicense: true,
      icon: '🌡️',
      active: true
    },
    {
      name: 'Flooring Installation',
      slug: 'flooring-installation',
      category: 'Flooring',
      description: 'Professional flooring installation',
      basePrice: '4.25',
      priceUnit: 'sqft',
      requiresLicense: false,
      icon: '🏠',
      active: true
    },
    {
      name: 'Landscaping',
      slug: 'landscaping',
      category: 'Landscaping',
      description: 'Lawn and garden maintenance',
      basePrice: '75.00',
      priceUnit: 'visit',
      requiresLicense: false,
      icon: '🌿',
      active: true
    },
    {
      name: 'Roofing Repairs',
      slug: 'roofing-repairs',
      category: 'Roofing',
      description: 'Professional roofing repair services',
      basePrice: '450.00',
      priceUnit: 'project',
      requiresLicense: true,
      icon: '🏠',
      active: true
    },
    {
      name: 'Handyman Services',
      slug: 'handyman',
      category: 'General',
      description: 'General handyman services',
      basePrice: '85.00',
      priceUnit: 'hour',
      requiresLicense: false,
      icon: '🔨',
      active: true
    }
  ]
  
  const inserted = await db.insert(services).values(servicesData).returning()
  
  return inserted.reduce((acc, service) => {
    acc[service.slug] = service.id
    return acc
  }, {} as Record<string, string>)
}
```

## Query Helpers

```typescript
// packages/database/src/queries/vendors.ts

import { db } from '../client'
import { vendors, vendorDocuments, vendorServices } from '../schema/vendors'
import { eq, and, sql } from 'drizzle-orm'

export async function getVendorWithDocuments(vendorId: string) {
  const vendor = await db.query.vendors.findFirst({
    where: eq(vendors.id, vendorId),
    with: {
      documents: true,
      services: true,
      serviceAreas: true
    }
  })
  
  return vendor
}

export async function getVendorsForService(
  serviceId: string,
  zipCode: string
) {
  const result = await db
    .select()
    .from(vendors)
    .innerJoin(vendorServices, eq(vendors.id, vendorServices.vendorId))
    .innerJoin(vendorServiceAreas, eq(vendors.id, vendorServiceAreas.vendorId))
    .where(
      and(
        eq(vendorServices.serviceId, serviceId),
        eq(vendors.status, 'active'),
        sql`${vendorServiceAreas.zipCodes}::jsonb ? ${zipCode}`
      )
    )
  
  return result
}

export async function updateVendorMetrics(vendorId: string) {
  const metrics = await db.execute(sql`
    UPDATE vendors 
    SET 
      total_jobs_completed = (
        SELECT COUNT(*) 
        FROM projects 
        WHERE vendor_id = ${vendorId} 
        AND status = 'completed'
      ),
      average_rating = (
        SELECT AVG(overall_rating) 
        FROM reviews 
        WHERE reviewee_id = (
          SELECT user_id FROM vendors WHERE id = ${vendorId}
        )
      ),
      total_earnings = (
        SELECT COALESCE(SUM(amount), 0)
        FROM vendor_payouts
        WHERE vendor_id = ${vendorId}
        AND status = 'completed'
      )
    WHERE id = ${vendorId}
    RETURNING *
  `)
  
  return metrics
}
```

## Type Exports

```typescript
// packages/database/src/types.ts

import { InferSelectModel, InferInsertModel } from 'drizzle-orm'
import * as schema from './schema'

// User types
export type User = InferSelectModel<typeof schema.users>
export type NewUser = InferInsertModel<typeof schema.users>

export type UserProfile = InferSelectModel<typeof schema.userProfiles>
export type NewUserProfile = InferInsertModel<typeof schema.userProfiles>

// Property types
export type Property = InferSelectModel<typeof schema.properties>
export type NewProperty = InferInsertModel<typeof schema.properties>

// Vendor types
export type Vendor = InferSelectModel<typeof schema.vendors>
export type NewVendor = InferInsertModel<typeof schema.vendors>

export type VendorDocument = InferSelectModel<typeof schema.vendorDocuments>
export type NewVendorDocument = InferInsertModel<typeof schema.vendorDocuments>

// Project types
export type Project = InferSelectModel<typeof schema.projects>
export type NewProject = InferInsertModel<typeof schema.projects>

// Payment types
export type Payment = InferSelectModel<typeof schema.payments>
export type NewPayment = InferInsertModel<typeof schema.payments>

// Export all types
export * from './schema'
```

## Environment Configuration

```bash
# .env.local (Development)
DATABASE_URL="postgresql://postgres:password@localhost:5432/clearquotepro"
NODE_ENV="development"

# .env.production (Production - Neon)
DATABASE_URL="postgresql://user:<EMAIL>/clearquotepro?sslmode=require"
NODE_ENV="production"
```

## Database Commands

```bash
# Install dependencies
pnpm install

# Generate migrations
pnpm db:generate

# Run migrations
pnpm db:migrate

# Push schema directly (development)
pnpm db:push

# Seed database
pnpm db:seed

# Open Drizzle Studio
pnpm db:studio

# Drop all tables (careful!)
pnpm db:drop
```

## Connection Pooling for Production

```typescript
// packages/database/src/pool.ts

import { Pool } from 'pg'
import { drizzle } from 'drizzle-orm/node-postgres'
import * as schema from './schema'

// Production connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20, // Maximum connections
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
  ssl: {
    rejectUnauthorized: false
  }
})

export const db = drizzle(pool, { schema })
```

## Performance Monitoring

```typescript
// packages/database/src/monitoring.ts

import { db } from './client'
import { sql } from 'drizzle-orm'

export async function getDatabaseMetrics() {
  const metrics = await db.execute(sql`
    SELECT 
      (SELECT count(*) FROM pg_stat_activity) as active_connections,
      (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle') as idle_connections,
      (SELECT count(*) FROM pg_stat_activity WHERE wait_event_type IS NOT NULL) as waiting_queries,
      pg_database_size(current_database()) as database_size
  `)
  
  return metrics[0]
}

export async function getSlowQueries() {
  const queries = await db.execute(sql`
    SELECT 
      query,
      calls,
      total_exec_time,
      mean_exec_time,
      max_exec_time
    FROM pg_stat_statements
    WHERE mean_exec_time > 100
    ORDER BY mean_exec_time DESC
    LIMIT 10
  `)
  
  return queries
}
```

## Backup Strategy

```bash
# Backup script (backup.sh)
#!/bin/bash

# Development backup
pg_dump $DATABASE_URL > backups/backup_$(date +%Y%m%d_%H%M%S).sql

# Production backup (Neon handles automatically)
# Configure in Neon dashboard for:
# - Daily backups
# - 7-day retention
# - Point-in-time recovery
```

This complete implementation provides a production-ready database setup with Drizzle ORM for the Clear Quote Pro platform.