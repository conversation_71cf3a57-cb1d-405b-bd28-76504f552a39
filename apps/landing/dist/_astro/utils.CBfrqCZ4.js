function xe(e){var n,r,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(n=0;n<i;n++)e[n]&&(r=xe(e[n]))&&(o&&(o+=" "),o+=r)}else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}function Pe(){for(var e,n,r=0,o="",i=arguments.length;r<i;r++)(e=arguments[r])&&(n=xe(e))&&(o&&(o+=" "),o+=n);return o}const ne="-",Re=e=>{const n=Te(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:l=>{const u=l.split(ne);return u[0]===""&&u.length!==1&&u.shift(),we(u,n)||Ge(l)},getConflictingClassGroupIds:(l,u)=>{const p=r[l]||[];return u&&o[l]?[...p,...o[l]]:p}}},we=(e,n)=>{if(e.length===0)return n.classGroupId;const r=e[0],o=n.nextPart.get(r),i=o?we(e.slice(1),o):void 0;if(i)return i;if(n.validators.length===0)return;const m=e.join(ne);return n.validators.find(({validator:l})=>l(m))?.classGroupId},fe=/^\[(.+)\]$/,Ge=e=>{if(fe.test(e)){const n=fe.exec(e)[1],r=n?.substring(0,n.indexOf(":"));if(r)return"arbitrary.."+r}},Te=e=>{const{theme:n,classGroups:r}=e,o={nextPart:new Map,validators:[]};for(const i in r)re(r[i],o,i,n);return o},re=(e,n,r,o)=>{e.forEach(i=>{if(typeof i=="string"){const m=i===""?n:be(n,i);m.classGroupId=r;return}if(typeof i=="function"){if(Ee(i)){re(i(o),n,r,o);return}n.validators.push({validator:i,classGroupId:r});return}Object.entries(i).forEach(([m,l])=>{re(l,be(n,m),r,o)})})},be=(e,n)=>{let r=e;return n.split(ne).forEach(o=>{r.nextPart.has(o)||r.nextPart.set(o,{nextPart:new Map,validators:[]}),r=r.nextPart.get(o)}),r},Ee=e=>e.isThemeGetter,Ne=e=>{if(e<1)return{get:()=>{},set:()=>{}};let n=0,r=new Map,o=new Map;const i=(m,l)=>{r.set(m,l),n++,n>e&&(n=0,o=r,r=new Map)};return{get(m){let l=r.get(m);if(l!==void 0)return l;if((l=o.get(m))!==void 0)return i(m,l),l},set(m,l){r.has(m)?r.set(m,l):i(m,l)}}},te="!",se=":",Le=se.length,Ve=e=>{const{prefix:n,experimentalParseClassName:r}=e;let o=i=>{const m=[];let l=0,u=0,p=0,g;for(let k=0;k<i.length;k++){let y=i[k];if(l===0&&u===0){if(y===se){m.push(i.slice(p,k)),p=k+Le;continue}if(y==="/"){g=k;continue}}y==="["?l++:y==="]"?l--:y==="("?u++:y===")"&&u--}const h=m.length===0?i:i.substring(p),M=je(h),O=M!==h,F=g&&g>p?g-p:void 0;return{modifiers:m,hasImportantModifier:O,baseClassName:M,maybePostfixModifierPosition:F}};if(n){const i=n+se,m=o;o=l=>l.startsWith(i)?m(l.substring(i.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:l,maybePostfixModifierPosition:void 0}}if(r){const i=o;o=m=>r({className:m,parseClassName:i})}return o},je=e=>e.endsWith(te)?e.substring(0,e.length-1):e.startsWith(te)?e.substring(1):e,Oe=e=>{const n=Object.fromEntries(e.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const i=[];let m=[];return o.forEach(l=>{l[0]==="["||n[l]?(i.push(...m.sort(),l),m=[]):m.push(l)}),i.push(...m.sort()),i}},Fe=e=>({cache:Ne(e.cacheSize),parseClassName:Ve(e),sortModifiers:Oe(e),...Re(e)}),_e=/\s+/,Be=(e,n)=>{const{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:i,sortModifiers:m}=n,l=[],u=e.trim().split(_e);let p="";for(let g=u.length-1;g>=0;g-=1){const h=u[g],{isExternal:M,modifiers:O,hasImportantModifier:F,baseClassName:k,maybePostfixModifierPosition:y}=r(h);if(M){p=h+(p.length>0?" "+p:p);continue}let G=!!y,A=o(G?k.substring(0,y):k);if(!A){if(!G){p=h+(p.length>0?" "+p:p);continue}if(A=o(k),!A){p=h+(p.length>0?" "+p:p);continue}G=!1}const $=m(O).join(":"),_=F?$+te:$,T=_+A;if(l.includes(T))continue;l.push(T);const E=i(A,G);for(let I=0;I<E.length;++I){const B=E[I];l.push(_+B)}p=h+(p.length>0?" "+p:p)}return p};function We(){let e=0,n,r,o="";for(;e<arguments.length;)(n=arguments[e++])&&(r=ke(n))&&(o&&(o+=" "),o+=r);return o}const ke=e=>{if(typeof e=="string")return e;let n,r="";for(let o=0;o<e.length;o++)e[o]&&(n=ke(e[o]))&&(r&&(r+=" "),r+=n);return r};function $e(e,...n){let r,o,i,m=l;function l(p){const g=n.reduce((h,M)=>M(h),e());return r=Fe(g),o=r.cache.get,i=r.cache.set,m=u,u(p)}function u(p){const g=o(p);if(g)return g;const h=Be(p,r);return i(p,h),h}return function(){return m(We.apply(null,arguments))}}const f=e=>{const n=r=>r[e]||[];return n.isThemeGetter=!0,n},ye=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,ve=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Ue=/^\d+\/\d+$/,qe=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,He=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Je=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,Xe=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,De=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,L=e=>Ue.test(e),d=e=>!!e&&!Number.isNaN(Number(e)),S=e=>!!e&&Number.isInteger(Number(e)),ee=e=>e.endsWith("%")&&d(e.slice(0,-1)),C=e=>qe.test(e),Ke=()=>!0,Qe=e=>He.test(e)&&!Je.test(e),ze=()=>!1,Ye=e=>Xe.test(e),Ze=e=>De.test(e),eo=e=>!t(e)&&!s(e),oo=e=>V(e,Se,ze),t=e=>ye.test(e),R=e=>V(e,Ae,Qe),oe=e=>V(e,ao,d),ge=e=>V(e,Ce,ze),ro=e=>V(e,Me,Ze),D=e=>V(e,Ie,Ye),s=e=>ve.test(e),W=e=>j(e,Ae),to=e=>j(e,io),he=e=>j(e,Ce),so=e=>j(e,Se),no=e=>j(e,Me),K=e=>j(e,Ie,!0),V=(e,n,r)=>{const o=ye.exec(e);return o?o[1]?n(o[1]):r(o[2]):!1},j=(e,n,r=!1)=>{const o=ve.exec(e);return o?o[1]?n(o[1]):r:!1},Ce=e=>e==="position"||e==="percentage",Me=e=>e==="image"||e==="url",Se=e=>e==="length"||e==="size"||e==="bg-size",Ae=e=>e==="length",ao=e=>e==="number",io=e=>e==="family-name",Ie=e=>e==="shadow",lo=()=>{const e=f("color"),n=f("font"),r=f("text"),o=f("font-weight"),i=f("tracking"),m=f("leading"),l=f("breakpoint"),u=f("container"),p=f("spacing"),g=f("radius"),h=f("shadow"),M=f("inset-shadow"),O=f("text-shadow"),F=f("drop-shadow"),k=f("blur"),y=f("perspective"),G=f("aspect"),A=f("ease"),$=f("animate"),_=()=>["auto","avoid","all","avoid-page","page","left","right","column"],T=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...T(),s,t],I=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto","contain","none"],c=()=>[s,t,p],v=()=>[L,"full","auto",...c()],ae=()=>[S,"none","subgrid",s,t],ie=()=>["auto",{span:["full",S,s,t]},S,s,t],U=()=>[S,"auto",s,t],le=()=>["auto","min","max","fr",s,t],Q=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],N=()=>["start","end","center","stretch","center-safe","end-safe"],z=()=>["auto",...c()],P=()=>[L,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...c()],a=()=>[e,s,t],ce=()=>[...T(),he,ge,{position:[s,t]}],de=()=>["no-repeat",{repeat:["","x","y","space","round"]}],me=()=>["auto","cover","contain",so,oo,{size:[s,t]}],Y=()=>[ee,W,R],x=()=>["","none","full",g,s,t],w=()=>["",d,W,R],q=()=>["solid","dashed","dotted","double"],pe=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],b=()=>[d,ee,he,ge],ue=()=>["","none",k,s,t],H=()=>["none",d,s,t],J=()=>["none",d,s,t],Z=()=>[d,s,t],X=()=>[L,"full",...c()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[C],breakpoint:[C],color:[Ke],container:[C],"drop-shadow":[C],ease:["in","out","in-out"],font:[eo],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[C],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[C],shadow:[C],spacing:["px",d],text:[C],"text-shadow":[C],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",L,t,s,G]}],container:["container"],columns:[{columns:[d,t,s,u]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:I()}],"overflow-x":[{"overflow-x":I()}],"overflow-y":[{"overflow-y":I()}],overscroll:[{overscroll:B()}],"overscroll-x":[{"overscroll-x":B()}],"overscroll-y":[{"overscroll-y":B()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:v()}],"inset-x":[{"inset-x":v()}],"inset-y":[{"inset-y":v()}],start:[{start:v()}],end:[{end:v()}],top:[{top:v()}],right:[{right:v()}],bottom:[{bottom:v()}],left:[{left:v()}],visibility:["visible","invisible","collapse"],z:[{z:[S,"auto",s,t]}],basis:[{basis:[L,"full","auto",u,...c()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[d,L,"auto","initial","none",t]}],grow:[{grow:["",d,s,t]}],shrink:[{shrink:["",d,s,t]}],order:[{order:[S,"first","last","none",s,t]}],"grid-cols":[{"grid-cols":ae()}],"col-start-end":[{col:ie()}],"col-start":[{"col-start":U()}],"col-end":[{"col-end":U()}],"grid-rows":[{"grid-rows":ae()}],"row-start-end":[{row:ie()}],"row-start":[{"row-start":U()}],"row-end":[{"row-end":U()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":le()}],"auto-rows":[{"auto-rows":le()}],gap:[{gap:c()}],"gap-x":[{"gap-x":c()}],"gap-y":[{"gap-y":c()}],"justify-content":[{justify:[...Q(),"normal"]}],"justify-items":[{"justify-items":[...N(),"normal"]}],"justify-self":[{"justify-self":["auto",...N()]}],"align-content":[{content:["normal",...Q()]}],"align-items":[{items:[...N(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...N(),{baseline:["","last"]}]}],"place-content":[{"place-content":Q()}],"place-items":[{"place-items":[...N(),"baseline"]}],"place-self":[{"place-self":["auto",...N()]}],p:[{p:c()}],px:[{px:c()}],py:[{py:c()}],ps:[{ps:c()}],pe:[{pe:c()}],pt:[{pt:c()}],pr:[{pr:c()}],pb:[{pb:c()}],pl:[{pl:c()}],m:[{m:z()}],mx:[{mx:z()}],my:[{my:z()}],ms:[{ms:z()}],me:[{me:z()}],mt:[{mt:z()}],mr:[{mr:z()}],mb:[{mb:z()}],ml:[{ml:z()}],"space-x":[{"space-x":c()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":c()}],"space-y-reverse":["space-y-reverse"],size:[{size:P()}],w:[{w:[u,"screen",...P()]}],"min-w":[{"min-w":[u,"screen","none",...P()]}],"max-w":[{"max-w":[u,"screen","none","prose",{screen:[l]},...P()]}],h:[{h:["screen","lh",...P()]}],"min-h":[{"min-h":["screen","lh","none",...P()]}],"max-h":[{"max-h":["screen","lh",...P()]}],"font-size":[{text:["base",r,W,R]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,s,oe]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ee,t]}],"font-family":[{font:[to,t,n]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,s,t]}],"line-clamp":[{"line-clamp":[d,"none",s,oe]}],leading:[{leading:[m,...c()]}],"list-image":[{"list-image":["none",s,t]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",s,t]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:a()}],"text-color":[{text:a()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...q(),"wavy"]}],"text-decoration-thickness":[{decoration:[d,"from-font","auto",s,R]}],"text-decoration-color":[{decoration:a()}],"underline-offset":[{"underline-offset":[d,"auto",s,t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:c()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",s,t]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",s,t]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ce()}],"bg-repeat":[{bg:de()}],"bg-size":[{bg:me()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},S,s,t],radial:["",s,t],conic:[S,s,t]},no,ro]}],"bg-color":[{bg:a()}],"gradient-from-pos":[{from:Y()}],"gradient-via-pos":[{via:Y()}],"gradient-to-pos":[{to:Y()}],"gradient-from":[{from:a()}],"gradient-via":[{via:a()}],"gradient-to":[{to:a()}],rounded:[{rounded:x()}],"rounded-s":[{"rounded-s":x()}],"rounded-e":[{"rounded-e":x()}],"rounded-t":[{"rounded-t":x()}],"rounded-r":[{"rounded-r":x()}],"rounded-b":[{"rounded-b":x()}],"rounded-l":[{"rounded-l":x()}],"rounded-ss":[{"rounded-ss":x()}],"rounded-se":[{"rounded-se":x()}],"rounded-ee":[{"rounded-ee":x()}],"rounded-es":[{"rounded-es":x()}],"rounded-tl":[{"rounded-tl":x()}],"rounded-tr":[{"rounded-tr":x()}],"rounded-br":[{"rounded-br":x()}],"rounded-bl":[{"rounded-bl":x()}],"border-w":[{border:w()}],"border-w-x":[{"border-x":w()}],"border-w-y":[{"border-y":w()}],"border-w-s":[{"border-s":w()}],"border-w-e":[{"border-e":w()}],"border-w-t":[{"border-t":w()}],"border-w-r":[{"border-r":w()}],"border-w-b":[{"border-b":w()}],"border-w-l":[{"border-l":w()}],"divide-x":[{"divide-x":w()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":w()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...q(),"hidden","none"]}],"divide-style":[{divide:[...q(),"hidden","none"]}],"border-color":[{border:a()}],"border-color-x":[{"border-x":a()}],"border-color-y":[{"border-y":a()}],"border-color-s":[{"border-s":a()}],"border-color-e":[{"border-e":a()}],"border-color-t":[{"border-t":a()}],"border-color-r":[{"border-r":a()}],"border-color-b":[{"border-b":a()}],"border-color-l":[{"border-l":a()}],"divide-color":[{divide:a()}],"outline-style":[{outline:[...q(),"none","hidden"]}],"outline-offset":[{"outline-offset":[d,s,t]}],"outline-w":[{outline:["",d,W,R]}],"outline-color":[{outline:a()}],shadow:[{shadow:["","none",h,K,D]}],"shadow-color":[{shadow:a()}],"inset-shadow":[{"inset-shadow":["none",M,K,D]}],"inset-shadow-color":[{"inset-shadow":a()}],"ring-w":[{ring:w()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:a()}],"ring-offset-w":[{"ring-offset":[d,R]}],"ring-offset-color":[{"ring-offset":a()}],"inset-ring-w":[{"inset-ring":w()}],"inset-ring-color":[{"inset-ring":a()}],"text-shadow":[{"text-shadow":["none",O,K,D]}],"text-shadow-color":[{"text-shadow":a()}],opacity:[{opacity:[d,s,t]}],"mix-blend":[{"mix-blend":[...pe(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":pe()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[d]}],"mask-image-linear-from-pos":[{"mask-linear-from":b()}],"mask-image-linear-to-pos":[{"mask-linear-to":b()}],"mask-image-linear-from-color":[{"mask-linear-from":a()}],"mask-image-linear-to-color":[{"mask-linear-to":a()}],"mask-image-t-from-pos":[{"mask-t-from":b()}],"mask-image-t-to-pos":[{"mask-t-to":b()}],"mask-image-t-from-color":[{"mask-t-from":a()}],"mask-image-t-to-color":[{"mask-t-to":a()}],"mask-image-r-from-pos":[{"mask-r-from":b()}],"mask-image-r-to-pos":[{"mask-r-to":b()}],"mask-image-r-from-color":[{"mask-r-from":a()}],"mask-image-r-to-color":[{"mask-r-to":a()}],"mask-image-b-from-pos":[{"mask-b-from":b()}],"mask-image-b-to-pos":[{"mask-b-to":b()}],"mask-image-b-from-color":[{"mask-b-from":a()}],"mask-image-b-to-color":[{"mask-b-to":a()}],"mask-image-l-from-pos":[{"mask-l-from":b()}],"mask-image-l-to-pos":[{"mask-l-to":b()}],"mask-image-l-from-color":[{"mask-l-from":a()}],"mask-image-l-to-color":[{"mask-l-to":a()}],"mask-image-x-from-pos":[{"mask-x-from":b()}],"mask-image-x-to-pos":[{"mask-x-to":b()}],"mask-image-x-from-color":[{"mask-x-from":a()}],"mask-image-x-to-color":[{"mask-x-to":a()}],"mask-image-y-from-pos":[{"mask-y-from":b()}],"mask-image-y-to-pos":[{"mask-y-to":b()}],"mask-image-y-from-color":[{"mask-y-from":a()}],"mask-image-y-to-color":[{"mask-y-to":a()}],"mask-image-radial":[{"mask-radial":[s,t]}],"mask-image-radial-from-pos":[{"mask-radial-from":b()}],"mask-image-radial-to-pos":[{"mask-radial-to":b()}],"mask-image-radial-from-color":[{"mask-radial-from":a()}],"mask-image-radial-to-color":[{"mask-radial-to":a()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":T()}],"mask-image-conic-pos":[{"mask-conic":[d]}],"mask-image-conic-from-pos":[{"mask-conic-from":b()}],"mask-image-conic-to-pos":[{"mask-conic-to":b()}],"mask-image-conic-from-color":[{"mask-conic-from":a()}],"mask-image-conic-to-color":[{"mask-conic-to":a()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ce()}],"mask-repeat":[{mask:de()}],"mask-size":[{mask:me()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",s,t]}],filter:[{filter:["","none",s,t]}],blur:[{blur:ue()}],brightness:[{brightness:[d,s,t]}],contrast:[{contrast:[d,s,t]}],"drop-shadow":[{"drop-shadow":["","none",F,K,D]}],"drop-shadow-color":[{"drop-shadow":a()}],grayscale:[{grayscale:["",d,s,t]}],"hue-rotate":[{"hue-rotate":[d,s,t]}],invert:[{invert:["",d,s,t]}],saturate:[{saturate:[d,s,t]}],sepia:[{sepia:["",d,s,t]}],"backdrop-filter":[{"backdrop-filter":["","none",s,t]}],"backdrop-blur":[{"backdrop-blur":ue()}],"backdrop-brightness":[{"backdrop-brightness":[d,s,t]}],"backdrop-contrast":[{"backdrop-contrast":[d,s,t]}],"backdrop-grayscale":[{"backdrop-grayscale":["",d,s,t]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d,s,t]}],"backdrop-invert":[{"backdrop-invert":["",d,s,t]}],"backdrop-opacity":[{"backdrop-opacity":[d,s,t]}],"backdrop-saturate":[{"backdrop-saturate":[d,s,t]}],"backdrop-sepia":[{"backdrop-sepia":["",d,s,t]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":c()}],"border-spacing-x":[{"border-spacing-x":c()}],"border-spacing-y":[{"border-spacing-y":c()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",s,t]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[d,"initial",s,t]}],ease:[{ease:["linear","initial",A,s,t]}],delay:[{delay:[d,s,t]}],animate:[{animate:["none",$,s,t]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[y,s,t]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:H()}],"rotate-x":[{"rotate-x":H()}],"rotate-y":[{"rotate-y":H()}],"rotate-z":[{"rotate-z":H()}],scale:[{scale:J()}],"scale-x":[{"scale-x":J()}],"scale-y":[{"scale-y":J()}],"scale-z":[{"scale-z":J()}],"scale-3d":["scale-3d"],skew:[{skew:Z()}],"skew-x":[{"skew-x":Z()}],"skew-y":[{"skew-y":Z()}],transform:[{transform:[s,t,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:X()}],"translate-x":[{"translate-x":X()}],"translate-y":[{"translate-y":X()}],"translate-z":[{"translate-z":X()}],"translate-none":["translate-none"],accent:[{accent:a()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:a()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",s,t]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":c()}],"scroll-mx":[{"scroll-mx":c()}],"scroll-my":[{"scroll-my":c()}],"scroll-ms":[{"scroll-ms":c()}],"scroll-me":[{"scroll-me":c()}],"scroll-mt":[{"scroll-mt":c()}],"scroll-mr":[{"scroll-mr":c()}],"scroll-mb":[{"scroll-mb":c()}],"scroll-ml":[{"scroll-ml":c()}],"scroll-p":[{"scroll-p":c()}],"scroll-px":[{"scroll-px":c()}],"scroll-py":[{"scroll-py":c()}],"scroll-ps":[{"scroll-ps":c()}],"scroll-pe":[{"scroll-pe":c()}],"scroll-pt":[{"scroll-pt":c()}],"scroll-pr":[{"scroll-pr":c()}],"scroll-pb":[{"scroll-pb":c()}],"scroll-pl":[{"scroll-pl":c()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",s,t]}],fill:[{fill:["none",...a()]}],"stroke-w":[{stroke:[d,W,R,oe]}],stroke:[{stroke:["none",...a()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},co=$e(lo);function mo(...e){return co(Pe(e))}export{Pe as a,mo as c};
