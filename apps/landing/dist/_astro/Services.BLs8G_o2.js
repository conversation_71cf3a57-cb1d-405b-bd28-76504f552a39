import{c as a,j as e}from"./createLucideIcon.CviQQVa0.js";import{C as h}from"./card.CpJhrToJ.js";import{B as l,H as x,S as f}from"./sparkles.p2bEQTzU.js";import{r as n}from"./index.RH_Wq4ov.js";import{S as g}from"./star.CsBUrWAP.js";import{S as d}from"./shield.DjzT6eBO.js";import{A as c}from"./arrow-right.Dqo4ztSu.js";import"./utils.CBfrqCZ4.js";/**
 * @license lucide-react v0.537.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y=[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]],b=a("droplets",y);/**
 * @license lucide-react v0.537.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N=[["path",{d:"m15 12-9.373 9.373a1 1 0 0 1-3.001-3L12 9",key:"1hayfq"}],["path",{d:"m18 15 4-4",key:"16gjal"}],["path",{d:"m21.5 11.5-1.914-1.914A2 2 0 0 1 19 8.172v-.344a2 2 0 0 0-.586-1.414l-1.657-1.657A6 6 0 0 0 12.516 3H9l1.243 1.243A6 6 0 0 1 12 8.485V10l2 2h1.172a2 2 0 0 1 1.414.586L18.5 14.5",key:"15ts47"}]],j=a("hammer",N);/**
 * @license lucide-react v0.537.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v=[["path",{d:"m14.622 17.897-10.68-2.913",key:"vj2p1u"}],["path",{d:"M18.376 2.622a1 1 0 1 1 3.002 3.002L17.36 9.643a.5.5 0 0 0 0 .707l.944.944a2.41 2.41 0 0 1 0 3.408l-.944.944a.5.5 0 0 1-.707 0L8.354 7.348a.5.5 0 0 1 0-.707l.944-.944a2.41 2.41 0 0 1 3.408 0l.944.944a.5.5 0 0 0 .707 0z",key:"18tc5c"}],["path",{d:"M9 8c-1.804 2.71-3.97 3.46-6.583 3.948a.507.507 0 0 0-.302.819l7.32 8.883a1 1 0 0 0 1.185.204C12.735 20.405 16 16.792 16 15",key:"ytzfxy"}]],$=a("paintbrush",v);/**
 * @license lucide-react v0.537.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k=[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],w=a("settings",k);/**
 * @license lucide-react v0.537.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]],_=a("thermometer",C);/**
 * @license lucide-react v0.537.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A=[["path",{d:"m17 14 3 3.3a1 1 0 0 1-.7 1.7H4.7a1 1 0 0 1-.7-1.7L7 14h-.3a1 1 0 0 1-.7-1.7L9 9h-.2A1 1 0 0 1 8 7.3L12 3l4 4.3a1 1 0 0 1-.8 1.7H15l3 3.3a1 1 0 0 1-.7 1.7H17Z",key:"cpyugq"}],["path",{d:"M12 22v-3",key:"kmzjlo"}]],S=a("tree-pine",A);/**
 * @license lucide-react v0.537.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H=[["path",{d:"M12.8 19.6A2 2 0 1 0 14 16H2",key:"148xed"}],["path",{d:"M17.5 8a2.5 2.5 0 1 1 2 4H2",key:"1u4tom"}],["path",{d:"M9.8 4.4A2 2 0 1 1 11 8H2",key:"75valh"}]],M=a("wind",H);/**
 * @license lucide-react v0.537.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z=[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.106-3.105c.32-.322.863-.22.983.218a6 6 0 0 1-8.259 7.057l-7.91 7.91a1 1 0 0 1-2.999-3l7.91-7.91a6 6 0 0 1 7.057-8.259c.438.12.54.662.219.984z",key:"1ngwbx"}]],L=a("wrench",z);/**
 * @license lucide-react v0.537.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],E=a("zap",q),I=[{name:"Interior Painting",price:"$1.50",unit:"per sq ft",icon:$,popular:!0,gradient:"from-terracotta/20 to-terracotta-light/20",iconColor:"text-terracotta"},{name:"Plumbing",price:"$125",unit:"per hour",icon:b,popular:!1,gradient:"from-teal/20 to-teal-light/20",iconColor:"text-teal"},{name:"Electrical",price:"$150",unit:"per hour",icon:E,popular:!1,gradient:"from-forest/20 to-forest-light/20",iconColor:"text-forest"},{name:"HVAC Service",price:"$189",unit:"full inspection",icon:M,popular:!0,gradient:"from-teal/20 to-ocean/20",iconColor:"text-teal"},{name:"Flooring",price:"$4.25",unit:"per sq ft",icon:x,popular:!1,gradient:"from-sage/20 to-sage/10",iconColor:"text-sage"},{name:"Handyman",price:"$85",unit:"per hour",icon:j,popular:!0,gradient:"from-forest/20 to-forest-light/20",iconColor:"text-forest"},{name:"Landscaping",price:"$75",unit:"per visit",icon:S,popular:!1,gradient:"from-forest/20 to-sage/20",iconColor:"text-forest"},{name:"Deep Cleaning",price:"$0.35",unit:"per sq ft",icon:f,popular:!1,gradient:"from-teal/20 to-teal-light/20",iconColor:"text-teal"},{name:"Appliance Install",price:"$149",unit:"standard install",icon:w,popular:!1,gradient:"from-forest/20 to-forest-light/20",iconColor:"text-forest"},{name:"Gutter Cleaning",price:"$189",unit:"single story",icon:d,popular:!1,gradient:"from-terracotta/20 to-terracotta-light/20",iconColor:"text-terracotta"},{name:"HVAC Filter",price:"$49",unit:"replacement",icon:_,popular:!1,gradient:"from-teal/20 to-teal-light/20",iconColor:"text-teal"},{name:"General Repairs",price:"$95",unit:"per hour",icon:L,popular:!1,gradient:"from-forest/20 to-forest-light/20",iconColor:"text-forest"}];function F(){const[p,i]=n.useState(null),[r,m]=n.useState(!1);return n.useEffect(()=>{m(!0)},[]),e.jsxs("section",{className:"py-24 relative overflow-hidden",children:[e.jsxs("div",{className:"absolute inset-0 -z-10",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-cream/30 to-background"}),e.jsx("div",{className:"absolute inset-0 dot-pattern opacity-[0.015]"})]}),e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsxs("div",{className:`text-center mb-16 transition-all duration-1000 ${r?"opacity-100 translate-y-0":"opacity-0 -translate-y-4"}`,children:[e.jsxs(l,{variant:"secondary",className:"mb-4 bg-forest/10 text-forest border-forest/20",children:[e.jsx(g,{className:"w-3 h-3 mr-1 fill-current"}),"20+ Services Available"]}),e.jsxs("h2",{className:"mb-4",children:[e.jsx("span",{className:"text-foreground",children:"Every Service."}),e.jsx("span",{className:"gradient-text ml-3",children:"One Platform."})]}),e.jsx("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"Transparent pricing for all your home needs. No quotes, no surprises—just honest rates from verified professionals."})]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5",children:I.map((t,s)=>{const u=t.icon,o=p===s;return e.jsx("div",{className:`transition-all duration-700 ${r?"opacity-100 translate-y-0":"opacity-0 translate-y-4"}`,style:{transitionDelay:`${s*50}ms`},onMouseEnter:()=>i(s),onMouseLeave:()=>i(null),children:e.jsxs(h,{className:`
                      relative p-6 h-full cursor-pointer 
                      border transition-all duration-300 group
                      ${t.popular?"border-forest/30 shadow-forest/10 shadow-lg":"border-border hover:border-forest/20"}
                      hover:shadow-xl hover:-translate-y-1
                      ${o?"bg-gradient-to-br "+t.gradient:"bg-card"}
                    `,children:[t.popular&&e.jsx(l,{className:"absolute -top-2 -right-2 bg-gradient-warm text-white border-0 text-xs px-2 py-1",children:"Popular"}),e.jsx("div",{className:"mb-4",children:e.jsx("div",{className:`
                        inline-flex p-3 rounded-lg transition-all duration-300
                        ${o?"bg-white/80 shadow-lg scale-110":"bg-background/50"}
                      `,children:e.jsx(u,{className:`w-6 h-6 ${t.iconColor} transition-transform duration-300 ${o?"rotate-12":""}`})})}),e.jsx("h3",{className:"font-semibold text-foreground mb-3 group-hover:text-forest transition-colors",children:t.name}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("p",{className:`text-2xl font-bold transition-colors ${o?"text-forest":"text-foreground"}`,children:t.price}),e.jsx("p",{className:"text-sm text-muted-foreground",children:t.unit})]}),e.jsx("div",{className:`
                      absolute bottom-6 right-6 
                      transition-all duration-300
                      ${o?"opacity-100 translate-x-0":"opacity-0 translate-x-2"}
                    `,children:e.jsx(c,{className:"w-4 h-4 text-forest"})})]})},t.name)})}),e.jsxs("div",{className:`
            mt-16 text-center glass rounded-2xl p-8 
            transition-all duration-1000 delay-500
            ${r?"opacity-100 translate-y-0":"opacity-0 translate-y-4"}
          `,children:[e.jsx("h3",{className:"text-2xl font-semibold mb-3",children:"Can't find what you need?"}),e.jsx("p",{className:"text-muted-foreground mb-6",children:"We're constantly adding new services. Tell us what you're looking for."}),e.jsxs("button",{className:"px-6 py-3 bg-forest text-white rounded-lg hover:bg-forest-light transition-colors inline-flex items-center gap-2 hover-lift",children:["Request a Service",e.jsx(c,{className:"w-4 h-4"})]})]}),e.jsx("div",{className:`
            mt-12 text-center transition-all duration-1000 delay-700
            ${r?"opacity-100":"opacity-0"}
          `,children:e.jsxs("p",{className:"text-sm text-muted-foreground flex items-center justify-center gap-2",children:[e.jsx(d,{className:"w-3.5 h-3.5"}),"All contractors are licensed, insured, and background-checked"]})})]})})]})}export{F as default};
