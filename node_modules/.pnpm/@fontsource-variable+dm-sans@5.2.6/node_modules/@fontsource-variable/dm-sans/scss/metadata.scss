$id: 'dm-sans';
$family: 'DM Sans';
$category: sans-serif;
$subsets: (latin, latin-ext);
$weights: (100, 200, 300, 400, 500, 600, 700, 800, 900);
$styles: (italic, normal);
$axes: (
  ital: (
    default: 0,
    min: 0,
    max: 1,
    step: 1,
  ),
  opsz: (
    default: 14,
    min: 9,
    max: 40,
    step: 0.1,
  ),
  wght: (
    default: 400,
    min: 100,
    max: 1000,
    step: 1,
  ),
);
$defaults: (
	subset: latin,
	weight: 400,
	style: normal,
	axis: wght,
);
$unicode: (
    latin-ext: (U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF),
    latin: (U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD),
  );

$metadata: (
  id: $id,
  family: $family,
  category: $category,
  subsets: $subsets,
  weights: $weights,
  styles: $styles,
  axes: $axes,
  defaults: $defaults,
  unicode: $unicode,
) !default;